{"version": 3, "file": "preload.js", "sourceRoot": "", "sources": ["../electron/preload.ts"], "names": [], "mappings": ";;AAAA,uCAAsD;AA6BtD,kEAAkE;AAClE,qDAAqD;AACrD,MAAM,WAAW,GAAgB;IAC/B,6BAA6B;IAC7B,kBAAkB,EAAE,CAAC,OAAY,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,sBAAsB,EAAE,OAAO,CAAC;IAEzF,iCAAiC;IACjC,cAAc,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,kBAAkB,CAAC;IAC5D,kBAAkB,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,sBAAsB,CAAC;IACpE,qBAAqB,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,0BAA0B,CAAC;IAE3E,4BAA4B;IAC5B,qBAAqB,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,yBAAyB,CAAC;IAC1E,mBAAmB,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,uBAAuB,CAAC;IACtE,sBAAsB,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,0BAA0B,CAAC;IAE5E,qBAAqB;IACrB,QAAQ,EAAE,OAAO,CAAC,QAAQ;IAE1B,kBAAkB;IAClB,UAAU,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,iBAAiB,CAAC;IACvD,UAAU,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC;IAEtE,iBAAiB;IACjB,cAAc,EAAE,CAAC,OAAY,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC;IACjF,YAAY,EAAE,CAAC,KAAa,EAAE,OAAe,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,EAAE,OAAO,CAAC;CACvG,CAAC;AAEF,yCAAyC;AACzC,wBAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC"}