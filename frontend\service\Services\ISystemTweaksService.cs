using GhostTweaker.Service.Models;

namespace GhostTweaker.Service.Services;

public interface ISystemTweaksService : IDisposable
{
    Task<SystemHealthInfo> GetSystemHealthAsync();
    Task<TweakResult> ApplyNetworkTweaksAsync(bool enable);
    Task<TweakResult> ApplyFpsBoostTweaksAsync(bool enable);
    Task<TweakResult> ApplyRamOptimizationAsync();
    Task<TweakResult> ApplyAdvancedRegistryTweaksAsync(bool enable);
    Task<TweakResult> ApplyGameModeTweaksAsync(bool enable);
    Task<TweakResult> CreateSystemRestorePointAsync(string description);
    Task<TweakResult> BackupRegistryAsync(string backupName);
    Task<TweakResult> RestoreRegistryAsync(string backupName);
    Task<List<string>> GetAvailableBackupsAsync();

    // Timer Resolution
    Task<TimerResolutionInfo> GetTimerResolutionInfoAsync();
    Task<TweakResult> ApplyTimerResolutionAsync(bool enable);
}
