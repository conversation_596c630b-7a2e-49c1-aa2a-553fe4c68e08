using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using GhostTweaker.Service.Services;
using GhostTweaker.Service.Models;

namespace GhostTweaker.Service.Tests.Services;

public class SystemTweaksServiceTests
{
    private readonly Mock<ILogger<SystemTweaksService>> _mockLogger;
    private readonly SystemTweaksService _service;

    public SystemTweaksServiceTests()
    {
        _mockLogger = new Mock<ILogger<SystemTweaksService>>();
        _service = new SystemTweaksService(_mockLogger.Object);
    }

    [Fact]
    public async Task GetSystemHealthAsync_ReturnsValidHealthInfo()
    {
        // Act
        var result = await _service.GetSystemHealthAsync();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.CpuUsage >= 0);
        Assert.True(result.MemoryUsage >= 0);
        Assert.True(result.DiskUsage >= 0);
        Assert.True(result.ProcessCount > 0);
        Assert.True(result.Uptime.TotalMilliseconds > 0);
        Assert.True(result.Timestamp != default);
    }



    [Fact]
    public async Task ApplyRamOptimizationAsync_ReturnsSuccess()
    {
        // Act
        var result = await _service.ApplyRamOptimizationAsync();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Success);
        Assert.Contains("RAM optimization", result.Message);
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task ApplyNetworkTweaksAsync_ReturnsResult(bool enable)
    {
        // Act
        var result = await _service.ApplyNetworkTweaksAsync(enable);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Success); // Currently placeholder, should succeed
        Assert.Contains("Network tweaks", result.Message);
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task ApplyFpsBoostTweaksAsync_ReturnsResult(bool enable)
    {
        // Act
        var result = await _service.ApplyFpsBoostTweaksAsync(enable);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Success); // Currently placeholder, should succeed
        Assert.Contains("FPS boost", result.Message);
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task ApplyAdvancedRegistryTweaksAsync_ReturnsResult(bool enable)
    {
        // Act
        var result = await _service.ApplyAdvancedRegistryTweaksAsync(enable);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Success); // Currently placeholder, should succeed
        Assert.Contains("Advanced registry", result.Message);
    }

    [Fact]
    public async Task CreateSystemRestorePointAsync_ReturnsResult()
    {
        // Arrange
        var description = "Test restore point";

        // Act
        var result = await _service.CreateSystemRestorePointAsync(description);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Success); // Currently placeholder, should succeed
        Assert.Contains(description, result.Message);
    }

    [Fact]
    public async Task GetAvailableBackupsAsync_ReturnsEmptyList()
    {
        // Act
        var result = await _service.GetAvailableBackupsAsync();

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<string>>(result);
        // Currently returns empty list as placeholder
    }
}
