# Ghost Tweaker - Desktop Application

A modern desktop application for Windows system optimization built with Electron, Vue.js 3, and .NET 8.

## Architecture Overview

The Ghost Tweaker Frontend consists of two main components that work together as a single desktop application:

### Electron App (`app/`)
- **Technology**: Electron + Vue.js 3 + TypeScript + TailwindCSS + Pinia
- **Purpose**: User interface and application orchestration
- **Port**: 5173 (development)

### Local .NET Service (`service/`)
- **Technology**: .NET 8 + ASP.NET Core
- **Purpose**: System optimization and tweaking operations
- **Port**: 3001 (configurable)

## Quick Start

### Prerequisites
- Node.js 18+ and npm
- .NET 8 SDK
- Administrator privileges (for system tweaks)
- Git

### Installation

1. **Clone and navigate to frontend**
   ```bash
   git clone <repository-url>
   cd ghost-tweaker/frontend
   ```

2. **Install Electron app dependencies**
   ```bash
   cd app/
   npm install
   ```

3. **Build .NET service**
   ```bash
   cd ../service/
   dotnet restore
   dotnet build
   ```

### Development

#### Start Development Environment
```bash
# In frontend/app/ directory
npm run dev
```

This will:
- Start the Vite development server (Vue.js app)
- Launch Electron with hot reload
- The .NET service will be started automatically by Electron

#### Individual Components

**Electron App Only:**
```bash
cd app/
npm run dev:vite    # Start Vite dev server
npm run dev:electron # Start Electron (in another terminal)
```

**Local .NET Service Only:**
```bash
cd service/
dotnet run
```

### Building for Production

#### Build All Components
```bash
cd app/
npm run build
```

This will:
1. Build the Vue.js application
2. Compile Electron main process
3. Build the .NET service for production
4. Package everything together

#### Create Windows Installer
```bash
cd app/
npm run dist:win
```

The installer will be created in `app/dist/` directory.

## Project Structure

```
frontend/
├── app/                     # Electron + Vue.js Application
│   ├── electron/           # Electron main process
│   │   ├── main.ts         # Main process entry point
│   │   └── preload.ts      # Preload script for IPC
│   ├── src/                # Vue.js application
│   │   ├── stores/         # Pinia stores (auth, system)
│   │   ├── views/          # Vue components/pages
│   │   ├── router/         # Vue Router configuration
│   │   ├── assets/         # Static assets and styles
│   │   └── types/          # TypeScript type definitions
│   ├── public/             # Public assets
│   ├── dist/               # Built application (after build)
│   ├── dist-electron/      # Built Electron files
│   └── package.json        # App dependencies and scripts
└── service/                # Local .NET Service
    ├── Services/           # Business logic services
    ├── Models/             # Data models and DTOs
    ├── Controllers/        # API controllers
    ├── Program.cs          # Service entry point
    └── *.csproj           # .NET project file
```

## Key Features

### System Optimization
- Timer resolution adjustment for reduced input lag
- Network tweaks for lower latency
- FPS boost through service optimization
- RAM optimization and cleanup
- Advanced registry tweaks
- Windows Game Mode controls

### Game Management
- Automatic game detection (Steam, Epic, GOG, etc.)
- Game-specific optimization profiles
- Process priority and CPU affinity management
- Game launch tracking and statistics

### Monitoring & Analysis
- Real-time system metrics (CPU, RAM, GPU)
- Latency analysis (DPC, interrupt time, network ping)
- Built-in benchmarking tools
- Performance overlay (future feature)

### User Management
- Secure authentication with external API
- Profile synchronization and backup
- Settings persistence
- Cloud sync capabilities

## Development Scripts

### Electron App (`app/`)
```bash
npm run dev          # Start full development environment
npm run dev:vite     # Start Vite dev server only
npm run dev:electron # Start Electron only
npm run build        # Build for production
npm run build:vite   # Build Vue.js app only
npm run build:electron # Build Electron main process only
npm run dist         # Create distribution package
npm run dist:win     # Create Windows installer
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
npm run type-check   # Run TypeScript type checking
```

### .NET Service (`service/`)
```bash
dotnet run           # Start development server
dotnet build         # Build the service
dotnet publish -c Release -r win-x64 --self-contained  # Build for production
dotnet clean         # Clean build artifacts
```

## Configuration

### Environment Variables
Create `.env` file in `app/` directory:
```
VITE_API_BASE_URL=http://localhost:3000
VITE_SERVICE_PORT=3001
```

### .NET Service Configuration
The service configuration is handled in `appsettings.json` and `appsettings.Development.json`.

## Authentication

The desktop application authenticates with an external API server for user management:
- JWT-based authentication
- Secure token storage
- Automatic token refresh
- Offline mode support

## System Requirements

### Development
- Windows 10/11
- Node.js 18+
- .NET 8 SDK
- Administrator privileges
- 4GB RAM minimum
- 2GB free disk space

### End Users
- Windows 10/11 (64-bit)
- Administrator privileges
- 2GB RAM minimum
- 500MB free disk space
- Internet connection (for authentication)

## Security Considerations

### Elevated Privileges
The application requires administrator privileges to perform system tweaks. Security measures include:
- UAC prompts for sensitive operations
- Validation of all system modifications
- Backup creation before applying changes
- Rollback capabilities

### Data Protection
- Secure storage of authentication tokens
- Encrypted communication with external API
- Local data encryption for sensitive settings
- No storage of user passwords locally

## Troubleshooting

### Common Issues

1. **Application won't start**
   - Ensure administrator privileges
   - Check if .NET 8 runtime is installed
   - Verify port 3001 is available

2. **Build failures**
   - Clear node_modules and reinstall: `rm -rf node_modules && npm install`
   - Clean .NET build: `dotnet clean && dotnet build`
   - Check TypeScript errors: `npm run type-check`

3. **Authentication issues**
   - Verify external API server is running
   - Check network connectivity
   - Clear stored tokens and re-login

4. **System tweaks not working**
   - Ensure application is running as administrator
   - Check Windows version compatibility
   - Verify system restore point creation

### Debug Mode
Enable debug logging by setting environment variable:
```
NODE_ENV=development
```

### Logs Location
- Electron logs: `%APPDATA%/ghost-tweaker/logs/`
- .NET service logs: Console output in development

## Contributing

### Code Style
- TypeScript for all JavaScript code
- ESLint + Prettier for code formatting
- Vue.js 3 Composition API
- TailwindCSS for styling
- C# coding conventions for .NET service

### Testing
- Run frontend tests: `npm run test` (when implemented)
- Test .NET service: `dotnet test` (when implemented)
- Manual testing checklist in `TESTING.md`

### Pull Requests
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

[Add your license information here]

## Support

For issues and support:
1. Check the troubleshooting section above
2. Review the `TESTING.md` file
3. Create an issue in the repository
4. Contact the development team
