using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using GhostTweaker.Service.Models;
using System.Text.Json;

namespace GhostTweaker.Service.Services;

public class AuthenticationService : IAuthenticationService
{
    private readonly ILogger<AuthenticationService> _logger;
    private readonly ServiceConfiguration _config;
    private readonly HttpClient _httpClient;

    // In-memory token storage (in production, this should be encrypted and persisted)
    private string? _accessToken;
    private string? _refreshToken;
    private DateTime? _expiresAt;

    public AuthenticationService(
        ILogger<AuthenticationService> logger,
        IOptions<ServiceConfiguration> config,
        HttpClient httpClient)
    {
        _logger = logger;
        _config = config.Value;
        _httpClient = httpClient;
    }

    public async Task<AuthenticationResponse> LoginAsync(AuthenticationRequest request)
    {
        try
        {
            _logger.LogInformation("Attempting login for user: {Username}", request.Username);

            var loginData = new
            {
                username = request.Username,
                password = request.Password,
                rememberMe = request.RememberMe
            };

            var json = JsonSerializer.Serialize(loginData);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_config.AuthServerUrl}/api/auth/login", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var authResponse = JsonSerializer.Deserialize<AuthenticationResponse>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (authResponse != null && authResponse.Success)
                {
                    StoreTokens(authResponse.AccessToken!, authResponse.RefreshToken!, authResponse.ExpiresAt!.Value);
                    _logger.LogInformation("Login successful for user: {Username}", request.Username);
                    return authResponse;
                }
            }

            _logger.LogWarning("Login failed for user: {Username}. Status: {StatusCode}", request.Username, response.StatusCode);
            return new AuthenticationResponse
            {
                Success = false,
                Error = $"Login failed: {response.StatusCode}"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for user: {Username}", request.Username);
            return new AuthenticationResponse
            {
                Success = false,
                Error = "Authentication service unavailable"
            };
        }
    }

    public async Task<AuthenticationResponse> RefreshTokenAsync(string refreshToken)
    {
        try
        {
            _logger.LogInformation("Attempting token refresh");

            var refreshData = new { refreshToken };
            var json = JsonSerializer.Serialize(refreshData);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_config.AuthServerUrl}/api/auth/refresh", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var authResponse = JsonSerializer.Deserialize<AuthenticationResponse>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (authResponse != null && authResponse.Success)
                {
                    StoreTokens(authResponse.AccessToken!, authResponse.RefreshToken!, authResponse.ExpiresAt!.Value);
                    _logger.LogInformation("Token refresh successful");
                    return authResponse;
                }
            }

            _logger.LogWarning("Token refresh failed. Status: {StatusCode}", response.StatusCode);
            return new AuthenticationResponse
            {
                Success = false,
                Error = $"Token refresh failed: {response.StatusCode}"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            return new AuthenticationResponse
            {
                Success = false,
                Error = "Authentication service unavailable"
            };
        }
    }

    public async Task<TokenValidationResponse> ValidateTokenAsync(string accessToken)
    {
        try
        {
            _logger.LogDebug("Validating access token");

            var request = new HttpRequestMessage(HttpMethod.Get, $"{_config.AuthServerUrl}/api/auth/validate");
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(request);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var validationResponse = JsonSerializer.Deserialize<TokenValidationResponse>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return validationResponse ?? new TokenValidationResponse
                {
                    IsValid = false,
                    Error = "Invalid response format"
                };
            }

            return new TokenValidationResponse
            {
                IsValid = false,
                Error = $"Validation failed: {response.StatusCode}"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token validation");
            return new TokenValidationResponse
            {
                IsValid = false,
                Error = "Authentication service unavailable"
            };
        }
    }

    public async Task<bool> LogoutAsync(string accessToken)
    {
        try
        {
            _logger.LogInformation("Attempting logout");

            var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.AuthServerUrl}/api/auth/logout");
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(request);

            // Clear stored tokens regardless of server response
            ClearStoredTokens();

            _logger.LogInformation("Logout completed");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            ClearStoredTokens(); // Clear tokens even if logout request failed
            return false;
        }
    }

    public void StoreTokens(string accessToken, string refreshToken, DateTime expiresAt)
    {
        _accessToken = accessToken;
        _refreshToken = refreshToken;
        _expiresAt = expiresAt;
        _logger.LogDebug("Tokens stored successfully");
    }

    public (string? AccessToken, string? RefreshToken, DateTime? ExpiresAt) GetStoredTokens()
    {
        return (_accessToken, _refreshToken, _expiresAt);
    }

    public void ClearStoredTokens()
    {
        _accessToken = null;
        _refreshToken = null;
        _expiresAt = null;
        _logger.LogDebug("Tokens cleared");
    }

    public bool IsTokenExpired(DateTime expiresAt)
    {
        return DateTime.UtcNow.AddMinutes(_config.TokenRefreshThresholdMinutes) >= expiresAt;
    }
}
