using GhostTweaker.Service.Models;

namespace GhostTweaker.Service.Services;

public interface IAuthenticationService
{
    Task<AuthenticationResponse> LoginAsync(AuthenticationRequest request);
    Task<AuthenticationResponse> RefreshTokenAsync(string refreshToken);
    Task<TokenValidationResponse> ValidateTokenAsync(string accessToken);
    Task<bool> LogoutAsync(string accessToken);
    void StoreTokens(string accessToken, string refreshToken, DateTime expiresAt);
    (string? AccessToken, string? RefreshToken, DateTime? ExpiresAt) GetStoredTokens();
    void ClearStoredTokens();
    bool IsTokenExpired(DateTime expiresAt);
}
