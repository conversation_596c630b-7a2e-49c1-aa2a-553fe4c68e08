namespace GhostTweaker.Service.Models;

public class ServiceConfiguration
{
    public int Port { get; set; } = 3001;
    public string AuthServerUrl { get; set; } = "http://localhost:3000";
    public string JwtSecret { get; set; } = "";
    public int TokenRefreshThresholdMinutes { get; set; } = 5;
}

public class AuthenticationRequest
{
    public string Username { get; set; } = "";
    public string Password { get; set; } = "";
    public bool RememberMe { get; set; } = false;
}

public class AuthenticationResponse
{
    public bool Success { get; set; }
    public string? AccessToken { get; set; }
    public string? RefreshToken { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? Error { get; set; }
    public UserInfo? User { get; set; }
}

public class UserInfo
{
    public string Id { get; set; } = "";
    public string Username { get; set; } = "";
    public string Email { get; set; } = "";
    public string Role { get; set; } = "";
}

public class TokenValidationResponse
{
    public bool IsValid { get; set; }
    public bool NeedsRefresh { get; set; }
    public UserInfo? User { get; set; }
    public string? Error { get; set; }
}

public class IpcRequest
{
    public string Method { get; set; } = "";
    public Dictionary<string, object>? Parameters { get; set; }
    public string RequestId { get; set; } = Guid.NewGuid().ToString();
}

public class IpcResponse
{
    public bool Success { get; set; }
    public object? Data { get; set; }
    public string? Error { get; set; }
    public string RequestId { get; set; } = "";
}

public class SystemHealthInfo
{
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public int ProcessCount { get; set; }
    public TimeSpan Uptime { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class TweakResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = "";
    public string? Error { get; set; }
    public Dictionary<string, object>? Data { get; set; }
}

// Timer Resolution Models
public class TimerResolutionInfo
{
    public bool IsEnabled { get; set; }
    public double CurrentResolutionMs { get; set; }
    public double TargetResolutionMs { get; set; } = 1.0;
    public double? OriginalResolutionMs { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

// Application State Models
public class ApplicationState
{
    public TweakStates TweakStates { get; set; } = new();
    public TimerResolutionInfo TimerResolution { get; set; } = new();
    public SystemHealthInfo? SystemHealth { get; set; }
    public AuthenticationState AuthState { get; set; } = new();
    public ServiceStatus ServiceStatus { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class TweakStates
{
    public bool NetworkTweaksEnabled { get; set; }
    public bool FpsBoostEnabled { get; set; }
    public bool AdvancedRegistryEnabled { get; set; }
    public bool GameModeEnabled { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class AuthenticationState
{
    public bool IsAuthenticated { get; set; }
    public UserInfo? User { get; set; }
    public DateTime? TokenExpiresAt { get; set; }
    public bool NeedsRefresh { get; set; }
    public DateTime LastChecked { get; set; } = DateTime.UtcNow;
}

public class ServiceStatus
{
    public bool BackendServiceRunning { get; set; } = true; // Always true for local service
    public bool AuthServerConnected { get; set; }
    public DateTime LastAuthServerCheck { get; set; } = DateTime.UtcNow;
    public string? LastError { get; set; }
}

// User Action Models
public class UserAction
{
    public string ActionType { get; set; } = "";
    public Dictionary<string, object>? Parameters { get; set; }
    public string RequestId { get; set; } = Guid.NewGuid().ToString();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}


