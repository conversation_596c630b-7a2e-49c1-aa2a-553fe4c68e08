using Microsoft.Extensions.Logging;
using GhostTweaker.Service.Models;
using System.Diagnostics;
using System.Management;
using System.Runtime.InteropServices;
using System.Security.Principal;

namespace GhostTweaker.Service.Services;

public class SystemTweaksService : ISystemTweaksService, IDisposable
{
    private readonly ILogger<SystemTweaksService> _logger;
    private readonly ITimerResolutionService _timerResolutionService;

    // Disposal tracking
    private bool _disposed = false;

    public SystemTweaksService(ILogger<SystemTweaksService> logger, ITimerResolutionService timerResolutionService)
    {
        _logger = logger;
        _timerResolutionService = timerResolutionService;

        // Check if running with administrator privileges
        bool isAdmin = IsRunningAsAdministrator();
        _logger.LogInformation("Service running with administrator privileges: {IsAdmin}", isAdmin);
    }

    public async Task<SystemHealthInfo> GetSystemHealthAsync()
    {
        try
        {
            _logger.LogDebug("Getting system health information");

            // Get CPU usage
            double cpuUsage = 0;
            try
            {
                using var cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                cpuCounter.NextValue(); // First call returns 0
                await Task.Delay(100);
                cpuUsage = cpuCounter.NextValue();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get CPU usage");
            }

            // Get memory usage
            double memoryUsage = 0;
            try
            {
                var totalMemory = GetTotalPhysicalMemory();
                var availableMemory = GetAvailablePhysicalMemory();
                if (totalMemory > 0)
                {
                    memoryUsage = ((totalMemory - availableMemory) / (double)totalMemory) * 100;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get memory usage");
            }

            // Get disk usage for C: drive
            double diskUsage = 0;
            try
            {
                var driveInfo = new DriveInfo("C:");
                if (driveInfo.IsReady)
                {
                    var usedSpace = driveInfo.TotalSize - driveInfo.AvailableFreeSpace;
                    diskUsage = (usedSpace / (double)driveInfo.TotalSize) * 100;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get disk usage");
            }

            // Get process count
            int processCount = Process.GetProcesses().Length;

            // Get system uptime
            var uptime = TimeSpan.FromMilliseconds(Environment.TickCount64);

            return new SystemHealthInfo
            {
                CpuUsage = Math.Round(cpuUsage, 2),
                MemoryUsage = Math.Round(memoryUsage, 2),
                DiskUsage = Math.Round(diskUsage, 2),
                ProcessCount = processCount,
                Uptime = uptime
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system health information");
            return new SystemHealthInfo(); // Return default values
        }
    }

    public async Task<TweakResult> ApplyNetworkTweaksAsync(bool enable)
    {
        try
        {
            _logger.LogInformation("Applying network tweaks: {Enable}", enable);

            // Placeholder implementation
            await Task.Delay(100);

            return new TweakResult
            {
                Success = true,
                Message = enable ? "Network tweaks enabled successfully" : "Network tweaks disabled successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying network tweaks");
            return new TweakResult
            {
                Success = false,
                Error = ex.Message
            };
        }
    }

    public async Task<TweakResult> ApplyFpsBoostTweaksAsync(bool enable)
    {
        try
        {
            _logger.LogInformation("Applying FPS boost tweaks: {Enable}", enable);

            // Placeholder implementation
            await Task.Delay(100);

            return new TweakResult
            {
                Success = true,
                Message = enable ? "FPS boost tweaks enabled successfully" : "FPS boost tweaks disabled successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying FPS boost tweaks");
            return new TweakResult
            {
                Success = false,
                Error = ex.Message
            };
        }
    }

    public async Task<TweakResult> ApplyRamOptimizationAsync()
    {
        try
        {
            _logger.LogInformation("Applying RAM optimization");

            // Placeholder implementation
            await Task.Delay(500);

            return new TweakResult
            {
                Success = true,
                Message = "RAM optimization completed successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying RAM optimization");
            return new TweakResult
            {
                Success = false,
                Error = ex.Message
            };
        }
    }

    public async Task<TweakResult> ApplyAdvancedRegistryTweaksAsync(bool enable)
    {
        try
        {
            _logger.LogInformation("Applying advanced registry tweaks: {Enable}", enable);

            // Placeholder implementation
            await Task.Delay(200);

            return new TweakResult
            {
                Success = true,
                Message = enable ? "Advanced registry tweaks enabled successfully" : "Advanced registry tweaks disabled successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying advanced registry tweaks");
            return new TweakResult
            {
                Success = false,
                Error = ex.Message
            };
        }
    }

    public async Task<TweakResult> ApplyGameModeTweaksAsync(bool enable)
    {
        try
        {
            _logger.LogInformation("Applying game mode tweaks: {Enable}", enable);

            // Placeholder implementation
            await Task.Delay(150);

            return new TweakResult
            {
                Success = true,
                Message = enable ? "Game mode tweaks enabled successfully" : "Game mode tweaks disabled successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying game mode tweaks");
            return new TweakResult
            {
                Success = false,
                Error = ex.Message
            };
        }
    }

    public async Task<TweakResult> CreateSystemRestorePointAsync(string description)
    {
        try
        {
            _logger.LogInformation("Creating system restore point: {Description}", description);

            // Placeholder implementation
            await Task.Delay(1000);

            return new TweakResult
            {
                Success = true,
                Message = $"System restore point '{description}' created successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating system restore point");
            return new TweakResult
            {
                Success = false,
                Error = ex.Message
            };
        }
    }

    public async Task<TweakResult> BackupRegistryAsync(string backupName)
    {
        try
        {
            _logger.LogInformation("Backing up registry: {BackupName}", backupName);

            // Placeholder implementation
            await Task.Delay(800);

            return new TweakResult
            {
                Success = true,
                Message = $"Registry backup '{backupName}' created successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error backing up registry");
            return new TweakResult
            {
                Success = false,
                Error = ex.Message
            };
        }
    }

    public async Task<TweakResult> RestoreRegistryAsync(string backupName)
    {
        try
        {
            _logger.LogInformation("Restoring registry from backup: {BackupName}", backupName);

            // Placeholder implementation
            await Task.Delay(1200);

            return new TweakResult
            {
                Success = true,
                Message = $"Registry restored from backup '{backupName}' successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restoring registry");
            return new TweakResult
            {
                Success = false,
                Error = ex.Message
            };
        }
    }

    public async Task<List<string>> GetAvailableBackupsAsync()
    {
        try
        {
            _logger.LogInformation("Getting available registry backups");

            // Placeholder implementation
            await Task.Delay(100);

            return new List<string>
            {
                "Backup_2024-01-01",
                "Backup_2024-01-15",
                "Pre_Tweaks_Backup"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available backups");
            return new List<string>();
        }
    }

    /// <summary>
    /// Gets the total physical memory in bytes
    /// </summary>
    private ulong GetTotalPhysicalMemory()
    {
        using var searcher = new ManagementObjectSearcher("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem");
        foreach (ManagementObject obj in searcher.Get())
        {
            return Convert.ToUInt64(obj["TotalPhysicalMemory"]);
        }
        return 0;
    }

    /// <summary>
    /// Gets the available physical memory in bytes
    /// </summary>
    private ulong GetAvailablePhysicalMemory()
    {
        using var searcher = new ManagementObjectSearcher("SELECT AvailableBytes FROM Win32_PerfRawData_PerfOS_Memory");
        foreach (ManagementObject obj in searcher.Get())
        {
            return Convert.ToUInt64(obj["AvailableBytes"]);
        }
        return 0;
    }

    public async Task<TimerResolutionInfo> GetTimerResolutionInfoAsync()
    {
        try
        {
            return await _timerResolutionService.GetTimerResolutionInfoAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting timer resolution info");
            throw;
        }
    }

    public async Task<TweakResult> ApplyTimerResolutionAsync(bool enable)
    {
        try
        {
            _logger.LogInformation("Applying timer resolution: {Enable}", enable);
            return await _timerResolutionService.ApplyTimerResolutionAsync(enable);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying timer resolution");
            return new TweakResult
            {
                Success = false,
                Error = ex.Message
            };
        }
    }

    /// <summary>
    /// Checks if the current process is running with administrator privileges
    /// </summary>
    private bool IsRunningAsAdministrator()
    {
        var identity = WindowsIdentity.GetCurrent();
        var principal = new WindowsPrincipal(identity);
        return principal.IsInRole(WindowsBuiltInRole.Administrator);
    }

    /// <summary>
    /// Dispose of the service and clean up resources
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Protected dispose method
    /// </summary>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Cleanup timer resolution service
                _timerResolutionService?.Dispose();
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// Finalizer to ensure cleanup even if Dispose is not called
    /// </summary>
    ~SystemTweaksService()
    {
        Dispose(false);
    }
}
