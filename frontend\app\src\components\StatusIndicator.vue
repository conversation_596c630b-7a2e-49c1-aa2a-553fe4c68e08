<template>
  <div class="p-4 bg-black/20 rounded-lg">
    <div class="flex justify-between items-center">
      <span class="text-slate-300">{{ label }}</span>
      <div class="flex items-center space-x-2">
        <div :class="[
          'w-2 h-2 rounded-full',
          isConnected ? 'bg-green-400' : 'bg-red-400'
        ]"></div>
        <span :class="[
          'text-sm',
          isConnected ? 'text-green-400' : 'text-red-400'
        ]">
          {{ isConnected ? connectedText : disconnectedText }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  label: string
  isConnected: boolean
  connectedText?: string
  disconnectedText?: string
}>()
</script>
