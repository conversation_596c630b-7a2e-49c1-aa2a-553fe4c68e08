{"name": "ghost-tweaker", "version": "1.0.0", "description": "A Windows system optimization desktop application with Electron + Vue.js frontend and .NET backend service", "private": true, "scripts": {"dev": "npm run dev:frontend", "dev:frontend": "npm run dev:app", "dev:frontend:full": "npm run build:electron && npm run dev:app:windows", "dev:app": "cd frontend/app && bun run dev", "dev:app:windows": "cd frontend/app && powershell -Command \"$env:VITE_DEV_SERVER_URL='http://localhost:5173'; bun run dev\"", "dev:backend": "cd backend/server && bun run dev", "dev:all": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "build:electron": "cd frontend/app && npm run build:electron", "build:service": "cd frontend/service && dotnet build", "build:frontend": "cd frontend/app && bun run build", "build": "npm run build:service && npm run build:frontend", "install:frontend": "cd frontend/app && bun install", "install:backend": "cd backend/server && bun install", "install:all": "npm run install:frontend && npm run install:backend", "clean": "npm run clean:frontend && npm run clean:service", "clean:frontend": "cd frontend/app && rm -rf dist dist-electron node_modules", "clean:service": "cd frontend/service && dotnet clean", "test": "npm run test:frontend", "test:frontend": "cd frontend/app && bun run test"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "typescript": "^5.8.3"}, "workspaces": ["frontend/app", "backend/server"], "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/ghost-tweaker.git"}, "keywords": ["electron", "vue", "windows", "system-optimization", "desktop-app", "dotnet"], "author": "Your Name", "license": "MIT"}