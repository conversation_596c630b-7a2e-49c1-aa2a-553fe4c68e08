# Ghost Tweaker - Backend API Server

An external authentication and user management API server for the Ghost Tweaker desktop application, built with Express.js, TypeScript, and MongoDB.

## Overview

The Ghost Tweaker Backend provides secure authentication and user management services that are consumed by the desktop application. It runs independently and can be deployed to any cloud provider or on-premise server.

## Architecture

### Express.js API Server (`server/`)
- **Technology**: Express.js + TypeScript + MongoDB + JWT + Bun
- **Purpose**: Authentication, user management, and profile synchronization
- **Port**: 3000 (configurable)

### Future Extensions (`client/`)
- Shared TypeScript types
- API client libraries
- Common validation schemas
- SDK for third-party integrations

## Quick Start

### Prerequisites
- Bun runtime (or Node.js 18+)
- MongoDB (local or cloud)
- Git

### Installation

1. **Clone and navigate to backend**
   ```bash
   git clone <repository-url>
   cd ghost-tweaker/backend/server
   ```

2. **Install dependencies**
   ```bash
   bun install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Initialize database**
   ```bash
   bun run init-db
   ```

### Development

#### Start Development Server
```bash
bun run dev
```

The server will start on `http://localhost:3000` with hot reload enabled.

#### Production Mode
```bash
bun run start
```

## Environment Configuration

Create a `.env` file in the `server/` directory:

```env
# Server Configuration
NODE_ENV=development
PORT=3000

# Database
MONGODB_URI=mongodb://localhost:27017/ghost-tweaker

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# CORS Configuration
CORS_ORIGIN=http://localhost:5173,http://localhost:3001
```

### Production Environment Variables
```env
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb+srv://user:<EMAIL>/ghost-tweaker
JWT_SECRET=secure-random-secret-key
JWT_EXPIRES_IN=24h
CORS_ORIGIN=https://your-desktop-app-domain.com
```

## API Endpoints

### Health Check
- **GET** `/api/health` - Server health status

### Authentication
- **POST** `/api/auth/register` - Register new user
- **POST** `/api/auth/login` - User login
- **POST** `/api/auth/logout` - User logout

### User Profile
- **GET** `/api/profile` - Get user profile (requires auth)
- **PUT** `/api/profile` - Update user profile (requires auth)

## API Documentation

### Authentication Endpoints

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "user": {
    "id": "string",
    "username": "string",
    "email": "string"
  }
}
```

#### Login User
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "token": "jwt-token-string",
  "user": {
    "id": "string",
    "username": "string",
    "email": "string"
  }
}
```

#### Get Profile
```http
GET /api/profile
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "string",
    "username": "string",
    "email": "string",
    "profile": {
      "firstName": "string",
      "lastName": "string",
      "avatar": "string"
    },
    "createdAt": "2025-07-02T...",
    "lastLogin": "2025-07-02T..."
  }
}
```

## Database Schema

### User Collection
```typescript
{
  _id: ObjectId,
  username: string (unique),
  email: string (unique),
  password: string (hashed with bcrypt),
  profile: {
    firstName?: string,
    lastName?: string,
    avatar?: string
  },
  createdAt: Date,
  updatedAt: Date,
  lastLogin?: Date
}
```

## Development Scripts

```bash
# Development
bun run dev          # Start with hot reload
bun run start        # Start production server

# Database
bun run init-db      # Initialize database with test data

# Build & Test
bun run build        # Compile TypeScript
bun run lint         # Run ESLint
bun run test         # Run tests (when implemented)
```

## Project Structure

```
backend/
├── server/                  # Express.js API Server
│   ├── src/
│   │   ├── routes/         # API route handlers
│   │   │   ├── auth.ts     # Authentication routes
│   │   │   └── profile.ts  # Profile management routes
│   │   ├── models/         # MongoDB models
│   │   │   └── User.ts     # User model
│   │   ├── middleware/     # Express middleware
│   │   │   └── auth.ts     # JWT authentication middleware
│   │   ├── config/         # Configuration files
│   │   │   └── db.ts       # Database connection
│   │   ├── scripts/        # Utility scripts
│   │   │   └── init-db.ts  # Database initialization
│   │   └── index.ts        # Server entry point
│   ├── package.json        # Dependencies and scripts
│   ├── tsconfig.json       # TypeScript configuration
│   └── .env.example        # Environment variables template
└── client/                 # Future: Shared utilities
```

## Security Features

### Authentication
- JWT-based stateless authentication
- Secure password hashing with bcrypt (12 rounds)
- Token expiration and validation
- Protected route middleware

### API Security
- CORS configuration for allowed origins
- Request body size limits (10MB)
- Input validation and sanitization
- Secure HTTP headers (future enhancement)

### Database Security
- MongoDB connection with authentication
- Unique constraints on username and email
- Password hashing before storage
- No sensitive data in logs

## Deployment

### Cloud Deployment (Recommended)

#### Using Railway
1. Connect your GitHub repository
2. Set environment variables in Railway dashboard
3. Deploy automatically on push

#### Using DigitalOcean App Platform
1. Create new app from GitHub repository
2. Configure environment variables
3. Set up MongoDB database
4. Deploy

#### Using AWS/Azure/GCP
1. Set up virtual machine or container service
2. Install Bun runtime
3. Configure environment variables
4. Set up MongoDB (Atlas recommended)
5. Configure SSL/TLS certificate
6. Set up process manager (PM2)

### Environment Setup
```bash
# Install Bun
curl -fsSL https://bun.sh/install | bash

# Clone and setup
git clone <repository-url>
cd ghost-tweaker/backend/server
bun install

# Set environment variables
export NODE_ENV=production
export PORT=3000
export MONGODB_URI=your-mongodb-connection-string
export JWT_SECRET=your-secure-secret

# Start server
bun run start
```

### Process Management
```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start "bun run start" --name ghost-tweaker-api

# Save PM2 configuration
pm2 save
pm2 startup
```

## Monitoring and Logging

### Health Monitoring
The `/api/health` endpoint provides:
- Server status
- Uptime information
- Environment details
- Database connectivity (future)

### Logging
- Request/response logging in development
- Error logging with stack traces
- Configurable log levels
- Structured logging format

### Metrics (Future)
- Request rate and response times
- Error rates and types
- Database query performance
- User authentication metrics

## Testing

### Manual Testing
```bash
# Health check
curl http://localhost:3000/api/health

# Register user
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"test","email":"<EMAIL>","password":"password123"}'

# Login user
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"password123"}'
```

### Load Testing
```bash
# Install artillery
npm install -g artillery

# Run load test
artillery quick --count 10 --num 100 http://localhost:3000/api/health
```

## Troubleshooting

### Common Issues

1. **Server won't start**
   - Check if port 3000 is available
   - Verify environment variables are set
   - Ensure MongoDB is running and accessible

2. **Database connection errors**
   - Verify MongoDB URI format
   - Check network connectivity
   - Ensure database exists and user has permissions

3. **Authentication issues**
   - Verify JWT secret is set and consistent
   - Check token expiration settings
   - Ensure proper Authorization header format

4. **CORS errors**
   - Check CORS_ORIGIN environment variable
   - Verify desktop app origin is allowed
   - Test with different browsers

### Debug Mode
Set `NODE_ENV=development` for detailed logging and error messages.

### Logs
- Development: Console output with detailed information
- Production: Structured logs (configure external logging service)

## Contributing

### Code Style
- TypeScript for all code
- ESLint configuration provided
- Consistent error handling
- RESTful API design principles

### Adding New Endpoints
1. Create route handler in `src/routes/`
2. Add middleware if needed
3. Update API documentation
4. Add tests (when testing framework is set up)

### Database Changes
1. Update models in `src/models/`
2. Create migration script if needed
3. Update `init-db.ts` script
4. Test with fresh database

## Future Enhancements

### Planned Features
- User settings and preferences API
- Game statistics and analytics
- Software update notifications
- Multi-factor authentication
- OAuth integration (Google, Microsoft)
- API rate limiting
- Advanced user roles and permissions

### Client Library (`client/`)
- TypeScript SDK for desktop app
- Shared type definitions
- API client with automatic retry
- Validation schemas
- Mock server for testing

## License

[Add your license information here]

## Support

For issues and support:
1. Check the troubleshooting section
2. Review the `TESTING.md` file
3. Check server logs for errors
4. Create an issue in the repository
