# Ghost Tweaker Backend - Testing Guide

## Testing Checklist

### Prerequisites
- [ ] Bun runtime installed
- [ ] MongoDB running (local or cloud)
- [ ] Environment variables configured

### Initial Setup Testing

1. **Installation**
   ```bash
   cd server/
   bun install
   ```
   - [ ] All dependencies install without errors
   - [ ] No security vulnerabilities reported

2. **Database Initialization**
   ```bash
   bun run init-db
   ```
   - [ ] MongoDB connection successful
   - [ ] Default admin user created
   - [ ] Default test user created

3. **Development Environment**
   ```bash
   bun run dev
   ```
   - [ ] Server starts on port 3000
   - [ ] No startup errors in console
   - [ ] Health check endpoint responds

### API Testing

#### Health Check
```bash
curl http://localhost:3000/api/health
```
Expected response:
```json
{
  "status": "ok",
  "timestamp": "2025-07-02T...",
  "uptime": 123.456,
  "environment": "development"
}
```

#### User Registration
```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### User Login
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

#### Profile Access (with JWT token)
```bash
curl -X GET http://localhost:3000/api/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Database Testing

#### MongoDB Connection
- [ ] Database connects successfully
- [ ] Collections are created automatically
- [ ] Indexes are properly configured

#### User Model Testing
- [ ] User registration creates new document
- [ ] Password is properly hashed
- [ ] Unique constraints work (username, email)
- [ ] User login validates credentials
- [ ] JWT tokens are generated correctly

### Security Testing

#### Authentication
- [ ] Invalid credentials are rejected
- [ ] JWT tokens expire correctly
- [ ] Protected routes require valid tokens
- [ ] Malformed tokens are rejected

#### Input Validation
- [ ] SQL injection attempts are blocked
- [ ] XSS attempts are sanitized
- [ ] Invalid email formats are rejected
- [ ] Weak passwords are rejected

### Performance Testing

#### Load Testing
```bash
# Install artillery for load testing
npm install -g artillery

# Run load test
artillery quick --count 10 --num 100 http://localhost:3000/api/health
```

#### Database Performance
- [ ] User queries execute within acceptable time
- [ ] Database connections are properly pooled
- [ ] Memory usage remains stable under load

### Error Handling Testing

#### Invalid Requests
- [ ] 400 Bad Request for malformed JSON
- [ ] 401 Unauthorized for missing tokens
- [ ] 404 Not Found for invalid endpoints
- [ ] 500 Internal Server Error handling

#### Database Errors
- [ ] Connection failures are handled gracefully
- [ ] Duplicate key errors return appropriate messages
- [ ] Validation errors are properly formatted

### Environment Testing

#### Development Environment
- [ ] Hot reload works correctly
- [ ] Debug logging is enabled
- [ ] CORS allows localhost origins

#### Production Environment
- [ ] Environment variables are loaded
- [ ] Production database connection works
- [ ] CORS is properly configured
- [ ] Logging is appropriate for production

### Integration Testing

#### Desktop App Integration
- [ ] CORS allows desktop app requests
- [ ] Authentication flow works end-to-end
- [ ] Profile updates sync correctly
- [ ] Error responses are handled by frontend

### Deployment Testing

#### Build Process
```bash
bun run build
```
- [ ] TypeScript compiles without errors
- [ ] Build artifacts are generated
- [ ] No runtime errors in built version

#### Production Deployment
- [ ] Server starts in production mode
- [ ] Environment variables are loaded
- [ ] Database connection works
- [ ] SSL/HTTPS configuration (if applicable)
- [ ] Process manager integration (PM2, etc.)

### Monitoring and Logging

#### Health Monitoring
- [ ] Health endpoint returns correct status
- [ ] Database connectivity is monitored
- [ ] Error rates are tracked
- [ ] Response times are measured

#### Logging
- [ ] Request/response logging works
- [ ] Error logging captures stack traces
- [ ] Log levels are configurable
- [ ] Log rotation works (if configured)

### Troubleshooting Common Issues

#### Database Connection Issues
1. Check MongoDB URI format
2. Verify network connectivity
3. Check authentication credentials
4. Ensure database exists

#### JWT Token Issues
1. Verify JWT secret is set
2. Check token expiration settings
3. Validate token format
4. Ensure proper Authorization header

#### CORS Issues
1. Check allowed origins configuration
2. Verify request headers
3. Test with different browsers
4. Check preflight requests

#### Port Conflicts
1. Check if port 3000 is available
2. Configure alternative port
3. Check firewall settings
4. Verify process isn't already running

### Test Data Cleanup

#### After Testing
```bash
# Clear test users from database
bun run init-db --clean
```

#### Reset Database
```bash
# Drop and recreate database (development only)
mongosh ghost-tweaker --eval "db.dropDatabase()"
bun run init-db
```

### Automated Testing (Future)

#### Unit Tests
- [ ] Set up Jest or similar testing framework
- [ ] Test individual functions and modules
- [ ] Mock database connections
- [ ] Test error conditions

#### Integration Tests
- [ ] Test API endpoints end-to-end
- [ ] Test database operations
- [ ] Test authentication flows
- [ ] Test error handling

#### Continuous Integration
- [ ] Set up GitHub Actions or similar
- [ ] Run tests on every commit
- [ ] Test multiple Node.js versions
- [ ] Deploy to staging environment
