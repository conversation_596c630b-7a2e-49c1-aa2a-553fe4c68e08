<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <!-- Logo/Header -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-white mb-2">Ghost Tweaker</h1>
        <p class="text-slate-300">System Optimization Tool</p>
      </div>

      <!-- Auth Card -->
      <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/20">
        <!-- Tab Navigation -->
        <div class="flex mb-6 bg-black/20 rounded-lg p-1">
          <button
            @click="switchTab('login')"
            :class="[
              'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200',
              activeTab === 'login'
                ? 'bg-white text-slate-900 shadow-sm'
                : 'text-slate-300 hover:text-white'
            ]"
          >
            Login
          </button>
          <button
            @click="switchTab('register')"
            :class="[
              'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200',
              activeTab === 'register'
                ? 'bg-white text-slate-900 shadow-sm'
                : 'text-slate-300 hover:text-white'
            ]"
          >
            Register
          </button>
        </div>

        <!-- Error Message -->
        <div v-if="authStore.error" class="mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-red-400 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <div>
              <h4 class="text-red-200 font-medium text-sm mb-1">
                {{ activeTab === 'login' ? 'Login Failed' : 'Registration Failed' }}
              </h4>
              <p class="text-red-300 text-sm">{{ authStore.error }}</p>
              <div v-if="showErrorHelp" class="mt-2 text-red-300 text-xs">
                <p v-if="authStore.error.includes('email')">
                  Please enter a valid email address (e.g., <EMAIL>)
                </p>
                <p v-else-if="authStore.error.includes('password')">
                  Password must be at least 6 characters long
                </p>
                <p v-else-if="authStore.error.includes('username')">
                  Username must be 3-30 characters long and unique
                </p>
                <p v-else-if="authStore.error.includes('already')">
                  Try using a different email address or username
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Login Form -->
        <form v-if="activeTab === 'login'" @submit.prevent="handleLogin" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              Username or Email
            </label>
            <input
              v-model="loginForm.identifier"
              type="text"
              required
              class="w-full px-4 py-3 bg-black/20 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Enter username or email"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              Password
            </label>
            <input
              v-model="loginForm.password"
              type="password"
              required
              class="w-full px-4 py-3 bg-black/20 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Enter password"
            />
          </div>

          <div class="flex items-center">
            <input
              v-model="loginForm.rememberMe"
              type="checkbox"
              id="remember"
              class="w-4 h-4 text-purple-600 bg-black/20 border-white/20 rounded focus:ring-purple-500"
            />
            <label for="remember" class="ml-2 text-sm text-slate-300">
              Remember me
            </label>
          </div>

          <button
            type="submit"
            :disabled="authStore.isLoading"
            class="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-medium rounded-lg hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-slate-900 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <span v-if="authStore.isLoading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Signing in...
            </span>
            <span v-else>Sign In</span>
          </button>
        </form>

        <!-- Register Form -->
        <form v-if="activeTab === 'register'" @submit.prevent="handleRegister" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              Username
            </label>
            <input
              v-model="registerForm.username"
              type="text"
              required
              minlength="3"
              maxlength="30"
              class="w-full px-4 py-3 bg-black/20 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Choose a username"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              Email
            </label>
            <input
              v-model="registerForm.email"
              type="email"
              required
              class="w-full px-4 py-3 bg-black/20 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Enter your email"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              Password
            </label>
            <input
              v-model="registerForm.password"
              type="password"
              required
              minlength="6"
              class="w-full px-4 py-3 bg-black/20 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Create a password"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              Confirm Password
            </label>
            <input
              v-model="registerForm.confirmPassword"
              type="password"
              required
              minlength="6"
              :class="[
                'w-full px-4 py-3 bg-black/20 border rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:border-transparent',
                registerForm.confirmPassword && !passwordsMatch
                  ? 'border-red-500/50 focus:ring-red-500'
                  : 'border-white/20 focus:ring-purple-500'
              ]"
              placeholder="Confirm your password"
            />
            <p v-if="registerForm.confirmPassword && !passwordsMatch" class="mt-1 text-red-400 text-xs">
              Passwords do not match
            </p>
          </div>

          <button
            type="submit"
            :disabled="authStore.isLoading || !isRegisterFormValid"
            class="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-medium rounded-lg hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-slate-900 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <span v-if="authStore.isLoading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Creating account...
            </span>
            <span v-else>Create Account</span>
          </button>
        </form>

        <!-- System Status -->
        <div class="mt-6 pt-6 border-t border-white/20">
          <div class="flex items-center justify-between text-sm">
            <span class="text-slate-300">System Status:</span>
            <div class="flex space-x-2">
              <div class="flex items-center">
                <div :class="[
                  'w-2 h-2 rounded-full mr-1',
                  isAuthServerConnected ? 'bg-green-400' : 'bg-red-400'
                ]"></div>
                <span class="text-slate-400">Auth ({{ isAuthServerConnected ? 'ON' : 'OFF' }})</span>
              </div>
              <div class="flex items-center">
                <div :class="[
                  'w-2 h-2 rounded-full mr-1',
                  isBackendConnected ? 'bg-green-400' : 'bg-red-400'
                ]"></div>
                <span class="text-slate-400">Backend ({{ isBackendConnected ? 'ON' : 'OFF' }})</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useSystemStore } from '@/stores/system'

const router = useRouter()
const authStore = useAuthStore()
const systemStore = useSystemStore()

const activeTab = ref<'login' | 'register'>('login')

const loginForm = ref({
  identifier: '',
  password: '',
  rememberMe: false
})

const registerForm = ref({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// Service status state
const applicationState = ref<any>(null)
const isLoadingStatus = ref(false)
let statusInterval: number | null = null

// Computed properties for service status
const isBackendConnected = computed(() => applicationState.value?.serviceStatus?.backendServiceRunning ?? false)
const isAuthServerConnected = computed(() => applicationState.value?.serviceStatus?.authServerConnected ?? false)

// Computed properties for better error handling
const showErrorHelp = computed(() => {
  return authStore.error && (
    authStore.error.includes('email') ||
    authStore.error.includes('password') ||
    authStore.error.includes('username') ||
    authStore.error.includes('already')
  )
})

const passwordsMatch = computed(() => {
  return registerForm.value.password === registerForm.value.confirmPassword
})

const isRegisterFormValid = computed(() => {
  return registerForm.value.username.length >= 3 &&
         registerForm.value.email.includes('@') &&
         registerForm.value.password.length >= 6 &&
         passwordsMatch.value
})

const handleLogin = async () => {
  const result = await authStore.login(
    loginForm.value.identifier,
    loginForm.value.password,
    loginForm.value.rememberMe
  )

  if (result.success) {
    router.push('/dashboard')
  }
}

const handleRegister = async () => {
  const result = await authStore.register(
    registerForm.value.username,
    registerForm.value.email,
    registerForm.value.password
  )
  if (result.success) {
    router.push('/dashboard')
  }
}

// Clear errors when switching tabs
const switchTab = (tab: 'login' | 'register') => {
  activeTab.value = tab
  authStore.clearError()
}

// Load application state for service status
const loadApplicationState = async () => {
  try {
    isLoadingStatus.value = true
    applicationState.value = await systemStore.getApplicationState()
  } catch (error) {
    console.error('Failed to load application state:', error)
  } finally {
    isLoadingStatus.value = false
  }
}

onMounted(async () => {
  console.log('🔍 LoginView: onMounted called')
  // Initialize system status
  console.log('🔍 LoginView: Calling systemStore.initializeSystem()')
  await systemStore.initializeSystem()
  console.log('🔍 LoginView: systemStore.initializeSystem() completed')

  // Load application state for service status
  await loadApplicationState()

  // Set up periodic status updates
  statusInterval = window.setInterval(loadApplicationState, 5000) // Update every 5 seconds

  // Check if user is already authenticated
  const isAuthenticated = await authStore.checkAuthStatus()
  if (isAuthenticated) {
    router.push('/dashboard')
  }
})

onUnmounted(() => {
  console.log('🔍 LoginView: onUnmounted called')
  // Clean up status interval
  if (statusInterval) {
    clearInterval(statusInterval)
    statusInterval = null
  }
})
</script>
