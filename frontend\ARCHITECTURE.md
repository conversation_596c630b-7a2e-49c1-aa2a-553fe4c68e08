# Ghost Tweaker - Frontend Architecture Documentation

## Overview

Ghost Tweaker Frontend is a desktop application for Windows system optimization consisting of two main components:

### Frontend (Desktop Application)
- **Electron App** (`frontend/app/`) - Vue.js 3 + TypeScript + TailwindCSS user interface
- **Local .NET Service** (`frontend/service/`) - System tweaks and local operations

### Backend (External API Server)
- **Express.js API** (`backend/server/`) - Authentication and user management
- **Future Extensions** (`backend/client/`) - Shared utilities and client libraries

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                 Desktop Application                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  Electron App   │  │ Local Service   │  │  Vue.js UI      │ │
│  │  (Port 5173)    │  │  (Port 3001)    │  │  (Frontend)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
           │                      │                      │
           │                      │                      │
    ┌─────────────┐        ┌─────────────┐        ┌─────────────┐
    │ External    │        │   Windows   │        │    User     │
    │ API Server  │        │   System    │        │ Interface   │
    │(Port 3000)  │        │             │        │             │
    └─────────────┘        └─────────────┘        └─────────────┘
           │
    ┌─────────────┐
    │  MongoDB    │
    │  Database   │
    └─────────────┘
```

## Component Details

### 1. Frontend - Desktop Application

#### 1.1 Electron App (frontend/app/)

**Purpose**: User interface and application orchestration
**Technology Stack**: Electron + Vue.js 3 + TypeScript + TailwindCSS + Pinia

**Key Files**:
- `electron/main.ts` - Main Electron process, manages local service
- `electron/preload.ts` - Secure IPC bridge between main and renderer
- `src/stores/auth.ts` - Authentication state management
- `src/stores/system.ts` - System operations and local service communication
- `src/views/LoginView.vue` - Authentication interface
- `src/views/DashboardView.vue` - Main application interface
- `src/router/index.ts` - Route guards and navigation

**Responsibilities**:
- Start and manage local .NET service
- Communicate with external API server for authentication
- Provide secure IPC communication
- Handle user authentication flow
- Display system information and controls
- Manage application lifecycle

**Security Features**:
- Context isolation enabled
- Node integration disabled in renderer
- Secure preload script for IPC
- No direct access to Node.js APIs from renderer

#### 1.2 Local .NET Service (frontend/service/)

**Purpose**: Local system operations and Windows API access
**Technology Stack**: .NET 8 + TCP Server + Windows APIs

**Key Files**:
- `Program.cs` - Service entry point and dependency injection
- `Services/SystemTweaksService.cs` - Windows system modifications
- `Services/IpcServerService.cs` - TCP server for app communication
- `Models/ServiceConfiguration.cs` - Data models and DTOs

**Responsibilities**:
- Apply system optimizations (registry, services, power plans)
- Monitor system health (CPU, memory, disk usage)
- Provide TCP-based IPC server for Electron app
- Create system restore points and registry backups
- Handle Windows-specific operations requiring elevated privileges

### 2. .NET Backend Service (gt-backend)

**Purpose**: System tweaks, hardware monitoring, and business logic
**Technology Stack**: .NET 8 + TCP Server + Windows APIs

**Key Files**:
- `Program.cs` - Service entry point and dependency injection
- `Services/SystemTweaksService.cs` - Windows system modifications
- `Services/AuthenticationService.cs` - JWT token management
- `Services/IpcServerService.cs` - TCP server for frontend communication
- `Services/BackgroundService.cs` - Service orchestration
- `Models/ServiceConfiguration.cs` - Data models and DTOs

**Responsibilities**:
- Apply system optimizations (registry, services, power plans)
- Monitor system health (CPU, memory, disk usage)
- Manage JWT tokens and authentication state
- Provide TCP-based IPC server
- Create system restore points and registry backups

**System Tweaks Implemented**:
- Timer resolution optimization (1ms for reduced input lag)
- Network TCP optimizations (TcpAckFrequency, TcpNoDelay)
- FPS boost (disable services, high performance power plan)
- RAM optimization (clear standby list, pagefile settings)
- Advanced registry tweaks (SystemResponsiveness, GPU scheduling)
- Game mode and overlay controls

### 3. Express.js Authentication API (gt-server/server)

**Purpose**: User authentication and session management
**Technology Stack**: Express.js + TypeScript + MongoDB + JWT + bcrypt

**Key Files**:
- `src/index.ts` - Express server setup and middleware
- `src/routes/auth.ts` - Authentication endpoints
- `src/models/User.ts` - MongoDB user schema
- `src/middleware/auth.ts` - JWT verification middleware
- `src/config/db.ts` - MongoDB connection
- `src/scripts/init-db.ts` - Database initialization

**API Endpoints**:
- `POST /api/auth/login` - User login with username/email + password
- `POST /api/auth/register` - New user registration
- `POST /api/auth/refresh` - JWT token refresh
- `POST /api/auth/logout` - User logout and token invalidation
- `GET /api/auth/verify` - Token validity verification
- `GET /api/health` - Server health check

**Authentication Features**:
- JWT access tokens (1 hour expiry)
- JWT refresh tokens (7 days expiry)
- Secure password hashing with bcrypt
- Token refresh before expiration
- "Remember Me" functionality
- User role management (user/admin)

## Data Flow

### Authentication Flow
1. User opens application → Electron main process starts
2. Main process starts auth server and backend service
3. Vue.js app checks for stored JWT tokens
4. If tokens exist, verify with auth server
5. If valid, proceed to dashboard; if invalid, show login
6. User logs in → Auth server validates and returns JWT tokens
7. Tokens stored securely and used for subsequent requests
8. Auto-refresh tokens before expiration

### System Operations Flow
1. User triggers system tweak from dashboard
2. Vue.js app calls system store method
3. System store sends request to backend service via TCP
4. Backend service applies Windows system modifications
5. Result returned to frontend and displayed to user
6. System health monitoring updates in real-time

## IPC Communication

### Electron Main ↔ Renderer
- Secure IPC via preload script
- Methods: backend management, dialog boxes, app info
- No direct Node.js access in renderer process

### Frontend ↔ Backend Service
- TCP socket communication on port 3001
- JSON message format with request/response pattern
- Methods: system.health, tweaks.*, system.*

### Frontend ↔ Auth Server
- HTTP REST API on port 3000
- JWT bearer token authentication
- Standard HTTP status codes and JSON responses

## State Management

### Frontend (Pinia Stores)
- `auth.ts` - User authentication state, token management
- `system.ts` - Backend communication, system health, tweak states
- Persistent storage for authentication tokens
- Reactive state updates across components

### Backend (.NET)
- In-memory service state
- Secure token storage in local app data
- Configuration via appsettings.json
- Dependency injection for service management

## Security Considerations

### Authentication
- JWT tokens with short expiry times
- Secure refresh token rotation
- Password hashing with bcrypt (12 rounds)
- Token storage in secure local storage

### System Access
- Backend service requires administrator privileges
- Registry modifications with proper error handling
- System restore point creation before major changes
- Service management with appropriate permissions

### IPC Security
- Context isolation in Electron
- Preload script whitelist for IPC methods
- No eval() or unsafe code execution
- Secure communication channels only

## Development Workflow

### Setup
1. `npm run install:all` - Install all dependencies
2. `npm run init-db` - Initialize database with default users
3. `npm run dev` - Start all services in development mode

### Building
1. `npm run build` - Build all components
2. `npm run dist:win` - Create Windows installer

### Testing
- Frontend: Vue Test Utils + Vitest
- Backend: xUnit + .NET Test SDK
- Integration: Manual testing of complete flow

## File Structure Explanation

```
ghost-tweaker/
├── gt-client/client/          # Electron + Vue.js Frontend
│   ├── electron/              # Electron main process files
│   ├── src/
│   │   ├── stores/           # Pinia state management
│   │   ├── views/            # Vue.js pages/components
│   │   ├── router/           # Vue Router with auth guards
│   │   ├── types/            # TypeScript definitions
│   │   └── assets/           # Static assets and styles
│   ├── public/               # Public assets
│   └── package.json          # Frontend dependencies and scripts
├── gt-backend/               # .NET Backend Service
│   ├── Services/             # Business logic services
│   ├── Models/               # Data models and DTOs
│   ├── Program.cs            # Service entry point
│   ├── appsettings.json      # Configuration
│   └── *.csproj             # .NET project file
├── gt-server/server/         # Express.js Authentication API
│   ├── src/
│   │   ├── routes/           # Express route handlers
│   │   ├── models/           # MongoDB schemas
│   │   ├── middleware/       # Express middleware
│   │   ├── config/           # Database and app config
│   │   └── scripts/          # Utility scripts
│   └── package.json          # API dependencies and scripts
├── GhostTweaker/             # Legacy WPF application (reference)
├── package.json              # Root package.json with orchestration scripts
├── README.md                 # User documentation
└── ARCHITECTURE.md           # This file - technical documentation
```

## Environment Variables

### Auth Server (.env or environment)
- `PORT` - Server port (default: 3000)
- `MONGODB_URI` - MongoDB connection string
- `JWT_ACCESS_SECRET` - JWT access token secret
- `JWT_REFRESH_SECRET` - JWT refresh token secret
- `NODE_ENV` - Environment (development/production)

### Backend Service (appsettings.json)
- `AuthServer:Url` - Auth server URL
- `Service:Port` - IPC server port
- `Service:TokenRefreshThresholdMinutes` - Token refresh timing

## Deployment Considerations

### Development
- All services run locally
- Hot reload for frontend and auth server
- Manual restart required for .NET backend changes

### Production
- Electron app packages all components
- Auth server and backend service bundled with installer
- MongoDB connection to cloud or local instance
- Windows installer with proper permissions

## Future Enhancements

### Planned Features
- Real-time system monitoring dashboard
- Game profile management and detection
- Cloud sync for user settings
- Advanced benchmarking tools
- Process priority and affinity management

### Technical Improvements
- WebSocket communication for real-time updates
- Automated testing suite
- CI/CD pipeline
- Code signing for production builds
- Auto-updater implementation

This architecture provides a solid foundation for a modern desktop application with proper separation of concerns, security, and maintainability.
