# Ghost Tweaker

A Windows system optimization desktop application with separate frontend and backend components.

## Project Structure

This repository contains two independent projects:

### 🖥️ Frontend - Desktop Application
**Location**: [`frontend/`](./frontend/)
- **Electron App**: Vue.js 3 + TypeScript + TailwindCSS user interface
- **Local Service**: .NET 8 service for system tweaks and optimizations
- **Deployment**: Single Windows desktop installer

### 🌐 Backend - API Server
**Location**: [`backend/`](./backend/)
- **API Server**: Express.js + TypeScript + MongoDB authentication server
- **Deployment**: Independent cloud/server deployment

## Development Setup

### Prerequisites

- **Node.js** (v18+) and **Bun** package manager
- **.NET 8 SDK** for the backend service
- **Windows** (required for system optimization features)

### Initial Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ghost-tweaker
   ```

2. **Install frontend dependencies**
   ```bash
   cd frontend/app
   bun install
   ```

3. **Build the .NET service**
   ```bash
   cd ../service
   dotnet build
   ```

4. **Start the external auth server** (in a separate terminal)
   ```bash
   cd ../../backend
   # Follow backend-specific setup instructions
   ```

## Development Workflow

### ⚠️ Critical Development Commands

The application has a specific build/development workflow that must be followed:

#### 1. For Electron Main/Preload Process Changes
When you modify files in `frontend/app/electron/`:
```bash
cd frontend/app
npm run build:electron
```
**Why:** Electron loads from compiled files in `dist-electron/`, not source TypeScript files.

#### 2. For Vue.js Frontend Development
```bash
cd frontend/app
$env:VITE_DEV_SERVER_URL="http://localhost:5173"; bun run dev
```
**Why:** The `VITE_DEV_SERVER_URL` environment variable is required for Electron to load the live Vite development server instead of static files.

#### 3. For .NET Service Changes
```bash
cd frontend/service
dotnet build
```
**Why:** The service executable needs to be recompiled for changes to take effect.

### Complete Development Startup Sequence

1. **Build Electron files** (if you made changes to main/preload):
   ```bash
   cd frontend/app
   npm run build:electron
   ```

2. **Start the external auth server** (separate terminal):
   ```bash
   cd backend
   # Start your auth server
   ```

3. **Start the frontend development server**:
   ```bash
   cd frontend/app
   $env:VITE_DEV_SERVER_URL="http://localhost:5173"; bun run dev
   ```

## Troubleshooting

### Button Flickering Issues
If UI buttons flicker during development:
- Ensure you're using `transition-colors` instead of `transition-all` in CSS
- Verify that `VITE_DEV_SERVER_URL` is set correctly in `.env.development`
- Check that Electron is loading from the live Vite server, not static files
- Ensure Electron main process loads environment variables with `dotenv`

### Code Changes Not Reflected
If your code changes aren't appearing:
1. **For Vue.js changes**: Ensure `VITE_DEV_SERVER_URL="http://localhost:5173"` is set
2. **For Electron changes**: Run `npm run build:electron` before starting dev server
3. **For .NET changes**: Run `dotnet build` in the service directory

### Service Connection Issues
- Verify the .NET service is compiled and running
- Check that the auth server is accessible on `localhost:3000`
- Ensure no other applications are using ports 3000 or 3001

## Communication Architecture

- **Frontend ↔ Local Service**: Pure TCP communication via Electron IPC
- **Local Service ↔ Auth Server**: HTTP requests to external server
- **Frontend never directly contacts external services** - all communication is routed through the local .NET service

## Documentation

- **Frontend**: [`frontend/README.md`](./frontend/README.md) - Desktop application setup and development
- **Backend**: [`backend/README.md`](./backend/README.md) - API server setup and deployment

## Key Features

- **System Optimization**: Timer resolution, network tweaks, FPS boost, RAM optimization
- **Game Management**: Auto-detection, profiles, priority management, statistics
- **User Authentication**: Secure JWT-based authentication with external API
- **Modern UI**: Electron + Vue.js 3 + TypeScript + TailwindCSS

## Technology Stack

- **Frontend**: Electron + Vue.js 3 + .NET 8 + TypeScript + TailwindCSS
- **Backend**: Express.js + TypeScript + MongoDB + JWT + Bun

## License

[Add your license information here]