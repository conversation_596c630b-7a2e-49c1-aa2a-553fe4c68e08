using GhostTweaker.Service.Models;

namespace GhostTweaker.Service.Services;

public interface ITimerResolutionService : IDisposable
{
    /// <summary>
    /// Gets the current timer resolution information
    /// </summary>
    Task<TimerResolutionInfo> GetTimerResolutionInfoAsync();

    /// <summary>
    /// Applies or removes timer resolution tweaks
    /// </summary>
    /// <param name="enable">True to enable high resolution (1ms), false to restore default</param>
    Task<TweakResult> ApplyTimerResolutionAsync(bool enable);

    /// <summary>
    /// Gets the current system timer resolution in milliseconds
    /// </summary>
    Task<double> GetCurrentSystemResolutionAsync();

    /// <summary>
    /// Checks if timer resolution is currently enabled by this service
    /// </summary>
    bool IsTimerResolutionEnabled { get; }

    /// <summary>
    /// Event fired when timer resolution state changes
    /// </summary>
    event EventHandler<TimerResolutionInfo>? TimerResolutionChanged;
}
