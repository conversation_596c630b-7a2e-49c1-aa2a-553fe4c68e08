# Ghost Tweaker - Backend Architecture Documentation

## Overview

Ghost Tweaker Backend is an external API server that provides authentication and user management services for the Ghost Tweaker desktop application.

### Backend (External API Server)
- **Express.js API** (`server/`) - Authentication and user management
- **Future Extensions** (`client/`) - Shared utilities and client libraries

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    Backend API Server                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  Express.js     │  │   JWT Auth      │  │   User Mgmt     │ │
│  │  (Port 3000)    │  │   Middleware    │  │   Routes        │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
           │
    ┌─────────────┐
    │  MongoDB    │
    │  Database   │
    └─────────────┘
```

## Component Details

### Express.js API Server (server/)

**Purpose**: External authentication and user management API
**Technology Stack**: Express.js + TypeScript + MongoDB + JWT + Bun

**Key Files**:
- `src/index.ts` - Main server entry point
- `src/routes/auth.ts` - Authentication routes (login, register, logout)
- `src/routes/profile.ts` - User profile management routes
- `src/models/User.ts` - MongoDB user model
- `src/middleware/auth.ts` - JWT authentication middleware
- `src/config/db.ts` - MongoDB connection configuration

**API Endpoints**:
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/profile` - Get user profile
- `PUT /api/profile` - Update user profile
- `GET /api/health` - Health check endpoint

**Authentication Flow**:
1. Desktop app sends login credentials to `/api/auth/login`
2. Server validates credentials against MongoDB
3. Server returns JWT token on successful authentication
4. Desktop app stores token and includes it in subsequent requests
5. Server validates JWT token on protected routes

## Technology Stack

### Core Technologies
- **Runtime**: Bun (JavaScript runtime)
- **Framework**: Express.js 5.x
- **Language**: TypeScript
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Password Hashing**: bcrypt

### Development Tools
- **Package Manager**: Bun
- **Type Checking**: TypeScript compiler
- **Linting**: ESLint
- **Development**: Hot reload with Bun --watch

## Database Schema

### User Collection
```typescript
{
  _id: ObjectId,
  username: string (unique),
  email: string (unique),
  password: string (hashed),
  profile: {
    firstName?: string,
    lastName?: string,
    avatar?: string
  },
  createdAt: Date,
  updatedAt: Date,
  lastLogin?: Date
}
```

## Security Features

### Authentication
- JWT-based stateless authentication
- Secure password hashing with bcrypt
- Token expiration and refresh handling

### API Security
- CORS configuration for desktop app origins
- Request rate limiting (future enhancement)
- Input validation and sanitization
- Secure HTTP headers

## Development Workflow

### Prerequisites
- Bun runtime
- MongoDB (local or cloud)
- Git

### Setup
```bash
cd server/
bun install
```

### Environment Configuration
Create `.env` file:
```
NODE_ENV=development
PORT=3000
MONGODB_URI=mongodb://localhost:27017/ghost-tweaker
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h
```

### Development Commands
```bash
# Start development server with hot reload
bun run dev

# Start production server
bun run start

# Build TypeScript
bun run build

# Initialize database
bun run init-db

# Run linting
bun run lint
```

## Deployment

### Production Environment
- Node.js/Bun runtime environment
- MongoDB database (MongoDB Atlas recommended)
- Environment variables configuration
- SSL/TLS certificate for HTTPS
- Process manager (PM2 or similar)

### Environment Variables
```
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb+srv://user:<EMAIL>/ghost-tweaker
JWT_SECRET=secure-random-secret
JWT_EXPIRES_IN=24h
CORS_ORIGIN=https://your-desktop-app-domain.com
```

### Deployment Steps
1. Build the application: `bun run build`
2. Set up production environment variables
3. Configure MongoDB connection
4. Deploy to cloud provider (AWS, DigitalOcean, etc.)
5. Set up SSL certificate
6. Configure domain and DNS
7. Set up monitoring and logging

## API Documentation

### Authentication Endpoints

#### POST /api/auth/register
Register a new user account.

**Request Body**:
```json
{
  "username": "string",
  "email": "string", 
  "password": "string"
}
```

**Response**:
```json
{
  "success": true,
  "message": "User registered successfully",
  "user": {
    "id": "string",
    "username": "string",
    "email": "string"
  }
}
```

#### POST /api/auth/login
Authenticate user and return JWT token.

**Request Body**:
```json
{
  "username": "string",
  "password": "string"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Login successful",
  "token": "jwt-token-string",
  "user": {
    "id": "string",
    "username": "string",
    "email": "string"
  }
}
```

### Profile Endpoints

#### GET /api/profile
Get current user profile (requires authentication).

**Headers**:
```
Authorization: Bearer <jwt-token>
```

**Response**:
```json
{
  "success": true,
  "user": {
    "id": "string",
    "username": "string",
    "email": "string",
    "profile": {
      "firstName": "string",
      "lastName": "string",
      "avatar": "string"
    }
  }
}
```

## Future Enhancements

### Planned Features
- User settings and preferences storage
- Usage analytics and telemetry
- Software update notifications
- Multi-factor authentication (MFA)
- OAuth integration (Google, Microsoft)
- API rate limiting and throttling
- Advanced user roles and permissions

### Backend Client Utilities (client/)
- Shared TypeScript types between frontend and backend
- API client libraries for desktop app
- Common validation schemas
- Shared utilities for API communication
- SDK for third-party integrations

## Monitoring and Maintenance

### Health Monitoring
- Health check endpoint at `/api/health`
- Database connection monitoring
- Performance metrics collection
- Error logging and alerting

### Maintenance Tasks
- Regular database backups
- Security updates and patches
- Performance optimization
- Log rotation and cleanup
- SSL certificate renewal

## Troubleshooting

### Common Issues
1. **Database Connection Errors**: Check MongoDB URI and network connectivity
2. **JWT Token Issues**: Verify JWT secret and token expiration settings
3. **CORS Errors**: Ensure desktop app origin is configured in CORS settings
4. **Port Conflicts**: Check if port 3000 is available or configure different port

### Debug Mode
Enable debug logging by setting `NODE_ENV=development` and checking server logs for detailed error information.
