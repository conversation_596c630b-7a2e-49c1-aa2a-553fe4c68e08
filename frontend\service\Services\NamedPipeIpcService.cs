using System.IO.Pipes;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Hosting;
using GhostTweaker.Service.Models;

namespace GhostTweaker.Service.Services;

public interface INamedPipeIpcService
{
    Task StartAsync(CancellationToken cancellationToken);
    Task StopAsync(CancellationToken cancellationToken);
    bool IsRunning { get; }
}

public class NamedPipeIpcService : INamedPipeIpcService
{
    private readonly ILogger<NamedPipeIpcService> _logger;
    private readonly ServiceConfiguration _config;
    private readonly IAuthenticationService _authService;
    private readonly ISystemTweaksService _tweaksService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IHostApplicationLifetime _hostLifetime;

    private readonly string _pipeName = "GhostTweakerIPC";
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _serverTask;
    private readonly JsonSerializerOptions _jsonOptions;

    public bool IsRunning { get; private set; }

    public NamedPipeIpcService(
        ILogger<NamedPipeIpcService> logger,
        IOptions<ServiceConfiguration> config,
        IAuthenticationService authService,
        ISystemTweaksService tweaksService,
        IHttpClientFactory httpClientFactory,
        IHostApplicationLifetime hostLifetime)
    {
        _logger = logger;
        _config = config.Value;
        _authService = authService;
        _tweaksService = tweaksService;
        _httpClientFactory = httpClientFactory;
        _hostLifetime = hostLifetime;

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            if (IsRunning)
            {
                _logger.LogWarning("Named Pipe IPC server is already running");
                return;
            }

            _logger.LogInformation("Starting Named Pipe IPC server with pipe name: {PipeName}", _pipeName);

            _cancellationTokenSource = new CancellationTokenSource();
            _serverTask = RunServerAsync(_cancellationTokenSource.Token);

            IsRunning = true;
            _logger.LogInformation("Named Pipe IPC server started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Named Pipe IPC server");
            throw;
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        try
        {
            if (!IsRunning)
            {
                _logger.LogWarning("Named Pipe IPC server is not running");
                return;
            }

            _logger.LogInformation("Stopping Named Pipe IPC server...");

            _cancellationTokenSource?.Cancel();

            if (_serverTask != null)
            {
                await _serverTask;
            }

            IsRunning = false;
            _logger.LogInformation("Named Pipe IPC server stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping Named Pipe IPC server");
            throw;
        }
    }

    private async Task RunServerAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                using var pipeServer = new NamedPipeServerStream(
                    _pipeName,
                    PipeDirection.InOut,
                    NamedPipeServerStream.MaxAllowedServerInstances,
                    PipeTransmissionMode.Message,
                    PipeOptions.Asynchronous);

                _logger.LogDebug("Waiting for client connection on pipe: {PipeName}", _pipeName);

                await pipeServer.WaitForConnectionAsync(cancellationToken);
                _logger.LogDebug("Client connected to Named Pipe");

                // Handle the client connection in a separate task
                _ = Task.Run(async () => await HandleClientAsync(pipeServer, cancellationToken), cancellationToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Named Pipe server cancelled");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Named Pipe server loop");
                await Task.Delay(1000, cancellationToken); // Wait before retrying
            }
        }
    }

    private async Task HandleClientAsync(NamedPipeServerStream pipeServer, CancellationToken cancellationToken)
    {
        try
        {
            using var reader = new StreamReader(pipeServer, Encoding.UTF8);
            using var writer = new StreamWriter(pipeServer, Encoding.UTF8) { AutoFlush = true };

            while (pipeServer.IsConnected && !cancellationToken.IsCancellationRequested)
            {
                var requestJson = await reader.ReadLineAsync();
                if (string.IsNullOrEmpty(requestJson))
                {
                    break;
                }

                _logger.LogDebug("Received Named Pipe request: {Request}", requestJson);

                var request = JsonSerializer.Deserialize<IpcRequest>(requestJson, _jsonOptions);
                if (request != null)
                {
                    var response = await ProcessRequestAsync(request);
                    var responseJson = JsonSerializer.Serialize(response, _jsonOptions);

                    await writer.WriteLineAsync(responseJson);
                    _logger.LogDebug("Sent Named Pipe response for request {RequestId}", request.RequestId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling Named Pipe client");
        }
        finally
        {
            try
            {
                if (pipeServer.IsConnected)
                {
                    pipeServer.Disconnect();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error disconnecting Named Pipe client");
            }
        }
    }

    private async Task<IpcResponse> ProcessRequestAsync(IpcRequest request)
    {
        try
        {
            _logger.LogInformation("Processing Named Pipe IPC request: Method='{Method}', RequestId='{RequestId}'",
                request.Method, request.RequestId);

            return request.Method.ToLowerInvariant() switch
            {
                "login" => await HandleLoginAsync(request),
                "logout" => await HandleLogoutAsync(request),
                "refresh-token" => await HandleRefreshTokenAsync(request),
                "validate-token" => await HandleValidateTokenAsync(request),
                "get-system-health" => await HandleGetSystemHealthAsync(request),
                "system.health" => await HandleGetSystemHealthAsync(request), // Support old method name
                "check-auth-server-health" => await HandleCheckAuthServerHealthAsync(request),
                "check-all-services-health" => await HandleCheckAllServicesHealthAsync(request),

                "apply-network-tweaks" => await HandleApplyNetworkTweaksAsync(request),
                "apply-fps-boost" => await HandleApplyFpsBoostAsync(request),
                "apply-ram-optimization" => await HandleApplyRamOptimizationAsync(request),
                "apply-advanced-registry" => await HandleApplyAdvancedRegistryAsync(request),
                "apply-game-mode" => await HandleApplyGameModeAsync(request),
                "create-restore-point" => await HandleCreateRestorePointAsync(request),
                "backup-registry" => await HandleBackupRegistryAsync(request),
                "restore-registry" => await HandleRestoreRegistryAsync(request),
                "get-available-backups" => await HandleGetAvailableBackupsAsync(request),
                "shutdown" => await HandleShutdownAsync(request),
                _ => new IpcResponse
                {
                    Success = false,
                    Error = $"Unknown method: {request.Method}",
                    RequestId = request.RequestId
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Named Pipe IPC request: {Method}", request.Method);
            return new IpcResponse
            {
                Success = false,
                Error = ex.Message,
                RequestId = request.RequestId
            };
        }
    }

    // Handler methods (copied from original IpcServerService)
    private async Task<IpcResponse> HandleLoginAsync(IpcRequest request)
    {
        var username = request.Parameters?.GetValueOrDefault("username")?.ToString() ?? "";
        var password = request.Parameters?.GetValueOrDefault("password")?.ToString() ?? "";
        var rememberMe = request.Parameters?.GetValueOrDefault("rememberMe")?.ToString() == "true";

        var authRequest = new AuthenticationRequest
        {
            Username = username,
            Password = password,
            RememberMe = rememberMe
        };

        var result = await _authService.LoginAsync(authRequest);
        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleLogoutAsync(IpcRequest request)
    {
        var accessToken = request.Parameters?.GetValueOrDefault("accessToken")?.ToString() ?? "";
        var result = await _authService.LogoutAsync(accessToken);

        return new IpcResponse
        {
            Success = result,
            Data = result,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleRefreshTokenAsync(IpcRequest request)
    {
        var refreshToken = request.Parameters?.GetValueOrDefault("refreshToken")?.ToString() ?? "";
        var result = await _authService.RefreshTokenAsync(refreshToken);

        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleValidateTokenAsync(IpcRequest request)
    {
        var accessToken = request.Parameters?.GetValueOrDefault("accessToken")?.ToString() ?? "";
        var result = await _authService.ValidateTokenAsync(accessToken);

        return new IpcResponse
        {
            Success = result.IsValid,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleGetSystemHealthAsync(IpcRequest request)
    {
        var result = await _tweaksService.GetSystemHealthAsync();
        return new IpcResponse
        {
            Success = true,
            Data = result,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleCheckAuthServerHealthAsync(IpcRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(10);

            var response = await httpClient.GetAsync($"{_config.AuthServerUrl}/api/health");
            var isHealthy = response.IsSuccessStatusCode;

            var healthData = new
            {
                IsHealthy = isHealthy,
                StatusCode = (int)response.StatusCode,
                Url = $"{_config.AuthServerUrl}/api/health",
                CheckedAt = DateTime.UtcNow
            };

            return new IpcResponse
            {
                Success = true,
                Data = healthData,
                RequestId = request.RequestId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking auth server health");
            var healthData = new
            {
                IsHealthy = false,
                StatusCode = 0,
                Url = $"{_config.AuthServerUrl}/api/health",
                CheckedAt = DateTime.UtcNow,
                Error = ex.Message
            };

            return new IpcResponse
            {
                Success = true,
                Data = healthData,
                RequestId = request.RequestId
            };
        }
    }

    private async Task<IpcResponse> HandleCheckAllServicesHealthAsync(IpcRequest request)
    {
        var authHealthTask = HandleCheckAuthServerHealthAsync(request);
        var systemHealthTask = HandleGetSystemHealthAsync(request);

        await Task.WhenAll(authHealthTask, systemHealthTask);

        var authHealth = await authHealthTask;
        var systemHealth = await systemHealthTask;

        var combinedHealth = new
        {
            AuthServer = authHealth.Data,
            System = systemHealth.Data,
            CheckedAt = DateTime.UtcNow
        };

        return new IpcResponse
        {
            Success = true,
            Data = combinedHealth,
            RequestId = request.RequestId
        };
    }



    private async Task<IpcResponse> HandleApplyNetworkTweaksAsync(IpcRequest request)
    {
        var enable = request.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyNetworkTweaksAsync(enable);
        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleApplyFpsBoostAsync(IpcRequest request)
    {
        var enable = request.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyFpsBoostTweaksAsync(enable);
        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleApplyRamOptimizationAsync(IpcRequest request)
    {
        var result = await _tweaksService.ApplyRamOptimizationAsync();
        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleApplyAdvancedRegistryAsync(IpcRequest request)
    {
        var enable = request.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyAdvancedRegistryTweaksAsync(enable);
        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleApplyGameModeAsync(IpcRequest request)
    {
        var enable = request.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyGameModeTweaksAsync(enable);
        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleCreateRestorePointAsync(IpcRequest request)
    {
        var description = request.Parameters?.GetValueOrDefault("description")?.ToString() ?? "Ghost Tweaker Restore Point";
        var result = await _tweaksService.CreateSystemRestorePointAsync(description);

        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleBackupRegistryAsync(IpcRequest request)
    {
        var backupName = request.Parameters?.GetValueOrDefault("backupName")?.ToString() ?? "GhostTweaker_Backup";
        var result = await _tweaksService.BackupRegistryAsync(backupName);
        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleRestoreRegistryAsync(IpcRequest request)
    {
        var backupName = request.Parameters?.GetValueOrDefault("backupName")?.ToString() ?? "";
        var result = await _tweaksService.RestoreRegistryAsync(backupName);

        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleGetAvailableBackupsAsync(IpcRequest request)
    {
        var result = await _tweaksService.GetAvailableBackupsAsync();
        return new IpcResponse
        {
            Success = true,
            Data = result,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleShutdownAsync(IpcRequest request)
    {
        _logger.LogInformation("Received shutdown request from Electron application");

        // Respond immediately to confirm shutdown request received
        var response = new IpcResponse
        {
            Success = true,
            Data = "Shutdown initiated",
            RequestId = request.RequestId
        };

        // Initiate graceful shutdown after a brief delay to allow response to be sent
        _ = Task.Run(async () =>
        {
            await Task.Delay(500); // Give time for response to be sent
            _logger.LogInformation("Initiating graceful application shutdown");
            _hostLifetime.StopApplication();
        });

        return response;
    }
}
