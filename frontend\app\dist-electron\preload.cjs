"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
const electronAPI = {
    // Core backend communication
    sendBackendRequest: (request) => electron_1.ipcRenderer.invoke('send-backend-request', request),
    // Basic service info (read-only)
    getBackendPort: () => electron_1.ipcRenderer.invoke('get-backend-port'),
    checkBackendStatus: () => electron_1.ipcRenderer.invoke('check-backend-status'),
    checkAuthServerStatus: () => electron_1.ipcRenderer.invoke('check-auth-server-status'),
    // Simple service management
    restartBackendService: () => electron_1.ipcRenderer.invoke('restart-backend-service'),
    validateAllServices: () => electron_1.ipcRenderer.invoke('validate-all-services'),
    retryServiceConnection: () => electron_1.ipcRenderer.invoke('retry-service-connection'),
    // System information
    platform: process.platform,
    // App information
    getVersion: () => electron_1.ipcRenderer.invoke('get-app-version'),
    getAppPath: (name) => electron_1.ipcRenderer.invoke('get-app-path', name),
    // Dialog methods
    showMessageBox: (options) => electron_1.ipcRenderer.invoke('show-message-box', options),
    showErrorBox: (title, content) => electron_1.ipcRenderer.invoke('show-error-box', title, content),
};
// Expose the API to the renderer process
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
//# sourceMappingURL=preload.js.map