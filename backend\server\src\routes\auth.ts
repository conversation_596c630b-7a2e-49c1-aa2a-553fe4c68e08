import { Router, Request, Response } from 'express';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import User, { IUser } from '../models/User';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// JWT secrets (in production, these should be environment variables)
const JWT_ACCESS_SECRET = process.env.JWT_ACCESS_SECRET || 'your_jwt_access_secret_change_in_production';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your_jwt_refresh_secret_change_in_production';
const ACCESS_TOKEN_EXPIRY = '1h';
const REFRESH_TOKEN_EXPIRY = '7d';

// Helper function to generate tokens
const generateTokens = (userId: string) => {
  const accessToken = jwt.sign({ userId }, JWT_ACCESS_SECRET, { expiresIn: ACCESS_TOKEN_EXPIRY });
  const refreshToken = jwt.sign({ userId }, JWT_REFRESH_SECRET, { expiresIn: REFRESH_TOKEN_EXPIRY });
  return { accessToken, refreshToken };
};

// Helper function to get user info without password
const getUserInfo = (user: IUser) => ({
  id: user._id,
  username: user.username,
  email: user.email,
  role: user.role,
  isPremium: user.isPremium
});

// POST /api/auth/register - New user registration
router.post('/register', async (req: Request, res: Response) => {
  try {
    const { username, email, password, confirmPassword } = req.body;

    // Validation
    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Username, email, and password are required'
      });
    }

    if (password !== confirmPassword) {
      return res.status(400).json({
        success: false,
        error: 'Passwords do not match'
      });
    }

    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        error: 'Password must be at least 6 characters long'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: existingUser.email === email ? 'Email already registered' : 'Username already taken'
      });
    }

    // Hash password and create user
    const hashedPassword = await bcrypt.hash(password, 12);
    const user = new User({
      username: username.trim(),
      email: email.trim().toLowerCase(),
      password: hashedPassword
    });

    await user.save();

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user._id.toString());

    // Store refresh token
    user.refreshTokens.push(refreshToken);
    await user.save();

    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

    res.status(201).json({
      success: true,
      accessToken,
      refreshToken,
      expiresAt,
      user: getUserInfo(user)
    });

  } catch (error: any) {
    console.error('Registration error:', error);

    // Handle Mongoose validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      return res.status(400).json({
        success: false,
        error: validationErrors.join(', ')
      });
    }

    // Handle duplicate key errors (unique constraint violations)
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      const fieldName = field === 'email' ? 'Email' : 'Username';
      return res.status(409).json({
        success: false,
        error: `${fieldName} already exists`
      });
    }

    res.status(500).json({
      success: false,
      error: 'Error registering user'
    });
  }
});

// POST /api/auth/login - User login with email/password or username/password
router.post('/login', async (req: Request, res: Response) => {
  try {
    const { username, email, password, rememberMe } = req.body;

    // Validation
    if ((!username && !email) || !password) {
      return res.status(400).json({
        success: false,
        error: 'Username/email and password are required'
      });
    }

    // Find user by email or username
    const user = await User.findOne({
      $or: [
        { email: email?.toLowerCase() },
        { username: username }
      ]
    });

    if (!user || !(await bcrypt.compare(password, user.password))) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user._id.toString());

    // Store refresh token if remember me is checked
    if (rememberMe) {
      user.refreshTokens.push(refreshToken);
      await user.save();
    }

    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

    res.json({
      success: true,
      accessToken,
      refreshToken: rememberMe ? refreshToken : undefined,
      expiresAt,
      user: getUserInfo(user)
    });

  } catch (error: any) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'Error logging in'
    });
  }
});

// POST /api/auth/refresh - JWT token refresh
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        error: 'Refresh token is required'
      });
    }

    // Verify refresh token
    let decoded: any;
    try {
      decoded = jwt.verify(refreshToken, JWT_REFRESH_SECRET);
    } catch (error) {
      return res.status(403).json({
        success: false,
        error: 'Invalid refresh token'
      });
    }

    // Find user and check if refresh token exists
    const user = await User.findById(decoded.userId);
    if (!user || !user.refreshTokens.includes(refreshToken)) {
      return res.status(403).json({
        success: false,
        error: 'Invalid refresh token'
      });
    }

    // Generate new tokens
    const { accessToken, refreshToken: newRefreshToken } = generateTokens(user._id.toString());

    // Replace old refresh token with new one
    user.refreshTokens = user.refreshTokens.filter(token => token !== refreshToken);
    user.refreshTokens.push(newRefreshToken);
    await user.save();

    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

    res.json({
      success: true,
      accessToken,
      refreshToken: newRefreshToken,
      expiresAt,
      user: getUserInfo(user)
    });

  } catch (error: any) {
    console.error('Token refresh error:', error);
    res.status(500).json({
      success: false,
      error: 'Error refreshing token'
    });
  }
});

// POST /api/auth/logout - User logout and token invalidation
router.post('/logout', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { refreshToken } = req.body;
    const userId = (req as any).user.userId;

    const user = await User.findById(userId);
    if (user && refreshToken) {
      // Remove the specific refresh token
      user.refreshTokens = user.refreshTokens.filter(token => token !== refreshToken);
      await user.save();
    }

    res.json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error: any) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Error logging out'
    });
  }
});

// GET /api/auth/verify - Verify current token validity
router.get('/verify', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.userId;
    const user = await User.findById(userId).select('-password -refreshTokens');

    if (!user) {
      return res.status(404).json({
        isValid: false,
        error: 'User not found'
      });
    }

    res.json({
      isValid: true,
      user: getUserInfo(user)
    });

  } catch (error: any) {
    console.error('Token verification error:', error);
    res.status(500).json({
      isValid: false,
      error: 'Error verifying token'
    });
  }
});

export default router;
