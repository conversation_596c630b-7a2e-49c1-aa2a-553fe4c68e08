using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Hosting;
using GhostTweaker.Service.Models;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;
using System.Linq;

namespace GhostTweaker.Service.Services;

public class IpcServerService : IIpcServerService
{
    private readonly ILogger<IpcServerService> _logger;
    private readonly ServiceConfiguration _config;
    private readonly IAuthenticationService _authService;
    private readonly ISystemTweaksService _tweaksService;
    private readonly IApplicationStateService _stateService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IHostApplicationLifetime _hostLifetime;

    private TcpListener? _tcpListener;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _serverTask;

    // JSON serializer options for case-insensitive property matching
    private readonly JsonSerializerOptions _jsonOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
    };

    public bool IsRunning { get; private set; }

    public IpcServerService(
        ILogger<IpcServerService> logger,
        IOptions<ServiceConfiguration> config,
        IAuthenticationService authService,
        ISystemTweaksService tweaksService,
        IApplicationStateService stateService,
        IHttpClientFactory httpClientFactory,
        IHostApplicationLifetime hostLifetime)
    {
        _logger = logger;
        _config = config.Value;
        _authService = authService;
        _tweaksService = tweaksService;
        _stateService = stateService;
        _httpClientFactory = httpClientFactory;
        _hostLifetime = hostLifetime;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            if (IsRunning)
            {
                _logger.LogWarning("IPC server is already running");
                return;
            }

            _logger.LogInformation("Starting IPC server on port {Port}", _config.Port);

            _tcpListener = new TcpListener(IPAddress.Loopback, _config.Port);
            _tcpListener.Start();

            _cancellationTokenSource = new CancellationTokenSource();
            _serverTask = RunServerAsync(_cancellationTokenSource.Token);

            IsRunning = true;
            _logger.LogInformation("IPC server started successfully on port {Port}", _config.Port);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start IPC server");
            throw;
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        try
        {
            if (!IsRunning)
            {
                _logger.LogWarning("IPC server is not running");
                return;
            }

            _logger.LogInformation("Stopping IPC server");

            _cancellationTokenSource?.Cancel();
            _tcpListener?.Stop();

            if (_serverTask != null)
            {
                await _serverTask;
            }

            IsRunning = false;
            _logger.LogInformation("IPC server stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping IPC server");
            throw;
        }
    }

    private async Task RunServerAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested && _tcpListener != null)
        {
            try
            {
                var tcpClient = await _tcpListener.AcceptTcpClientAsync();
                _ = Task.Run(() => HandleClientAsync(tcpClient, cancellationToken), cancellationToken);
            }
            catch (ObjectDisposedException)
            {
                // Expected when stopping the server
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error accepting client connection");
            }
        }
    }

    private async Task HandleClientAsync(TcpClient client, CancellationToken cancellationToken)
    {
        try
        {
            using (client)
            using (var stream = client.GetStream())
            {
                var buffer = new byte[4096];
                var received = await stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);

                if (received > 0)
                {
                    var requestData = Encoding.UTF8.GetString(buffer, 0, received);
                    _logger.LogInformation("Received request ({Length} bytes)", received);

                    // Check if this is an HTTP request and reject it
                    if (requestData.StartsWith("POST") || requestData.StartsWith("GET") || requestData.StartsWith("PUT") || requestData.StartsWith("DELETE"))
                    {
                        _logger.LogWarning("🚫 REJECTED HTTP REQUEST - This is a pure TCP server!");
                        _logger.LogWarning("🚫 HTTP request details: {RequestData}", requestData.Substring(0, Math.Min(200, requestData.Length)));

                        // Send a simple rejection response and close connection
                        var rejectionMessage = "HTTP requests not supported. Use TCP JSON communication only.";
                        var rejectionBytes = Encoding.UTF8.GetBytes(rejectionMessage);
                        await stream.WriteAsync(rejectionBytes, 0, rejectionBytes.Length, cancellationToken);
                        return;
                    }

                    // Pure TCP server - only accept JSON requests
                    string requestJson = requestData;

                    if (string.IsNullOrEmpty(requestJson))
                    {
                        _logger.LogWarning("Empty request JSON");
                        return;
                    }

                    _logger.LogInformation("Processing JSON: {Json}", requestJson);

                    var request = JsonSerializer.Deserialize<IpcRequest>(requestJson, _jsonOptions);
                    if (request != null)
                    {
                        var response = await ProcessRequestAsync(request);
                        var responseJson = JsonSerializer.Serialize(response, _jsonOptions);

                        // Send pure TCP response (no HTTP support)
                        var responseBytes = Encoding.UTF8.GetBytes(responseJson);
                        await stream.WriteAsync(responseBytes, 0, responseBytes.Length, cancellationToken);
                        _logger.LogInformation("Sent TCP response for request {RequestId}", request.RequestId);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling client connection");
        }
    }

    private async Task<IpcResponse> ProcessRequestAsync(IpcRequest request)
    {
        try
        {
            _logger.LogInformation("Processing IPC request: Method='{Method}', RequestId='{RequestId}'", request.Method ?? "NULL", request.RequestId);

            if (string.IsNullOrEmpty(request.Method))
            {
                _logger.LogWarning("Received request with null or empty method");
                return new IpcResponse
                {
                    Success = false,
                    Error = "Method is required",
                    RequestId = request.RequestId
                };
            }

            return request.Method.ToLowerInvariant() switch
            {
                "login" => await HandleLoginAsync(request),
                "logout" => await HandleLogoutAsync(request),
                "refresh-token" => await HandleRefreshTokenAsync(request),
                "validate-token" => await HandleValidateTokenAsync(request),
                "get-system-health" => await HandleGetSystemHealthAsync(request),
                "system.health" => await HandleGetSystemHealthAsync(request), // Support old method name
                "check-auth-server-health" => await HandleCheckAuthServerHealthAsync(request),
                "check-all-services-health" => await HandleCheckAllServicesHealthAsync(request),

                // State management methods
                "get-application-state" => await HandleGetApplicationStateAsync(request),
                "get-tweak-states" => await HandleGetTweakStatesAsync(request),
                "get-timer-resolution-info" => await HandleGetTimerResolutionInfoAsync(request),
                "process-user-action" => await HandleProcessUserActionAsync(request),

                // Legacy individual tweak methods (for backward compatibility)
                "apply-network-tweaks" => await HandleApplyNetworkTweaksAsync(request),
                "apply-fps-boost" => await HandleApplyFpsBoostAsync(request),
                "apply-ram-optimization" => await HandleApplyRamOptimizationAsync(request),
                "apply-advanced-registry" => await HandleApplyAdvancedRegistryAsync(request),
                "apply-game-mode" => await HandleApplyGameModeAsync(request),
                "apply-timer-resolution" => await HandleApplyTimerResolutionAsync(request),
                "create-restore-point" => await HandleCreateRestorePointAsync(request),
                "backup-registry" => await HandleBackupRegistryAsync(request),
                "restore-registry" => await HandleRestoreRegistryAsync(request),
                "get-available-backups" => await HandleGetAvailableBackupsAsync(request),
                "shutdown" => await HandleShutdownAsync(request),
                _ => new IpcResponse
                {
                    Success = false,
                    Error = $"Unknown method: {request.Method}",
                    RequestId = request.RequestId
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing IPC request: {Method}", request.Method);
            return new IpcResponse
            {
                Success = false,
                Error = ex.Message,
                RequestId = request.RequestId
            };
        }
    }

    private async Task<IpcResponse> HandleLoginAsync(IpcRequest request)
    {
        var username = request.Parameters?.GetValueOrDefault("username")?.ToString() ?? "";
        var password = request.Parameters?.GetValueOrDefault("password")?.ToString() ?? "";
        var rememberMe = request.Parameters?.GetValueOrDefault("rememberMe")?.ToString() == "true";

        var authRequest = new AuthenticationRequest
        {
            Username = username,
            Password = password,
            RememberMe = rememberMe
        };

        var result = await _authService.LoginAsync(authRequest);

        // Update application state if login was successful
        if (result.Success && result.User != null)
        {
            var authState = new Models.AuthenticationState
            {
                IsAuthenticated = true,
                User = result.User,
                TokenExpiresAt = result.ExpiresAt,
                NeedsRefresh = false,
                LastChecked = DateTime.UtcNow
            };

            await _stateService.UpdateAuthenticationStateAsync(authState);
            _logger.LogInformation("Updated application state after successful login for user: {Username}", result.User.Username);
        }

        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleLogoutAsync(IpcRequest request)
    {
        var accessToken = request.Parameters?.GetValueOrDefault("accessToken")?.ToString() ?? "";
        var result = await _authService.LogoutAsync(accessToken);

        return new IpcResponse
        {
            Success = result,
            Data = result,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleRefreshTokenAsync(IpcRequest request)
    {
        var refreshToken = request.Parameters?.GetValueOrDefault("refreshToken")?.ToString() ?? "";
        var result = await _authService.RefreshTokenAsync(refreshToken);

        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleValidateTokenAsync(IpcRequest request)
    {
        var accessToken = request.Parameters?.GetValueOrDefault("accessToken")?.ToString() ?? "";
        var result = await _authService.ValidateTokenAsync(accessToken);

        return new IpcResponse
        {
            Success = result.IsValid,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleGetSystemHealthAsync(IpcRequest request)
    {
        var result = await _tweaksService.GetSystemHealthAsync();
        return new IpcResponse
        {
            Success = true,
            Data = result,
            RequestId = request.RequestId
        };
    }



    private async Task<IpcResponse> HandleCheckAuthServerHealthAsync(IpcRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(5); // 5 second timeout

            var response = await httpClient.GetAsync($"{_config.AuthServerUrl}/api/health");
            var isHealthy = response.IsSuccessStatusCode;

            var healthData = new
            {
                IsHealthy = isHealthy,
                StatusCode = (int)response.StatusCode,
                Url = $"{_config.AuthServerUrl}/api/health",
                CheckedAt = DateTime.UtcNow
            };

            return new IpcResponse
            {
                Success = true,
                Data = healthData,
                RequestId = request.RequestId
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Auth server health check failed");

            var healthData = new
            {
                IsHealthy = false,
                Error = ex.Message,
                Url = $"{_config.AuthServerUrl}/api/health",
                CheckedAt = DateTime.UtcNow
            };

            return new IpcResponse
            {
                Success = true, // Success means the check completed, not that the service is healthy
                Data = healthData,
                RequestId = request.RequestId
            };
        }
    }

    private async Task<IpcResponse> HandleCheckAllServicesHealthAsync(IpcRequest request)
    {
        try
        {
            // Check backend service (self)
            var backendHealth = new
            {
                IsHealthy = true, // If we're processing this request, the backend is healthy
                Service = "Backend",
                CheckedAt = DateTime.UtcNow
            };

            // Check auth server
            var authServerHealthResponse = await HandleCheckAuthServerHealthAsync(request);
            var authServerHealth = authServerHealthResponse.Data;

            var allServicesHealth = new
            {
                Backend = backendHealth,
                AuthServer = authServerHealth,
                OverallHealthy = authServerHealthResponse.Success &&
                               authServerHealth != null &&
                               ((dynamic)authServerHealth).IsHealthy,
                CheckedAt = DateTime.UtcNow
            };

            return new IpcResponse
            {
                Success = true,
                Data = allServicesHealth,
                RequestId = request.RequestId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking all services health");
            return new IpcResponse
            {
                Success = false,
                Error = ex.Message,
                RequestId = request.RequestId
            };
        }
    }



    private async Task<IpcResponse> HandleApplyNetworkTweaksAsync(IpcRequest request)
    {
        var enable = request.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyNetworkTweaksAsync(enable);

        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleApplyFpsBoostAsync(IpcRequest request)
    {
        var enable = request.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyFpsBoostTweaksAsync(enable);

        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleApplyRamOptimizationAsync(IpcRequest request)
    {
        var result = await _tweaksService.ApplyRamOptimizationAsync();
        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleApplyAdvancedRegistryAsync(IpcRequest request)
    {
        var enable = request.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyAdvancedRegistryTweaksAsync(enable);

        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleApplyGameModeAsync(IpcRequest request)
    {
        var enable = request.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyGameModeTweaksAsync(enable);

        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleCreateRestorePointAsync(IpcRequest request)
    {
        var description = request.Parameters?.GetValueOrDefault("description")?.ToString() ?? "Ghost Tweaker Restore Point";
        var result = await _tweaksService.CreateSystemRestorePointAsync(description);

        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleBackupRegistryAsync(IpcRequest request)
    {
        var backupName = request.Parameters?.GetValueOrDefault("backupName")?.ToString() ?? $"Backup_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}";
        var result = await _tweaksService.BackupRegistryAsync(backupName);

        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleRestoreRegistryAsync(IpcRequest request)
    {
        var backupName = request.Parameters?.GetValueOrDefault("backupName")?.ToString() ?? "";
        var result = await _tweaksService.RestoreRegistryAsync(backupName);

        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleGetAvailableBackupsAsync(IpcRequest request)
    {
        var result = await _tweaksService.GetAvailableBackupsAsync();
        return new IpcResponse
        {
            Success = true,
            Data = result,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleGetApplicationStateAsync(IpcRequest request)
    {
        try
        {
            var state = await _stateService.GetApplicationStateAsync();
            return new IpcResponse
            {
                Success = true,
                Data = state,
                RequestId = request.RequestId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting application state");
            return new IpcResponse
            {
                Success = false,
                Error = ex.Message,
                RequestId = request.RequestId
            };
        }
    }

    private async Task<IpcResponse> HandleGetTweakStatesAsync(IpcRequest request)
    {
        try
        {
            var tweakStates = await _stateService.GetTweakStatesAsync();
            return new IpcResponse
            {
                Success = true,
                Data = tweakStates,
                RequestId = request.RequestId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tweak states");
            return new IpcResponse
            {
                Success = false,
                Error = ex.Message,
                RequestId = request.RequestId
            };
        }
    }

    private async Task<IpcResponse> HandleGetTimerResolutionInfoAsync(IpcRequest request)
    {
        try
        {
            var timerInfo = await _stateService.GetTimerResolutionInfoAsync();
            return new IpcResponse
            {
                Success = true,
                Data = timerInfo,
                RequestId = request.RequestId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting timer resolution info");
            return new IpcResponse
            {
                Success = false,
                Error = ex.Message,
                RequestId = request.RequestId
            };
        }
    }

    private async Task<IpcResponse> HandleProcessUserActionAsync(IpcRequest request)
    {
        try
        {
            var actionType = request.Parameters?.GetValueOrDefault("actionType")?.ToString() ?? "";
            var parameters = request.Parameters?.GetValueOrDefault("parameters") as Dictionary<string, object>;

            var userAction = new UserAction
            {
                ActionType = actionType,
                Parameters = parameters,
                RequestId = request.RequestId
            };

            var result = await _stateService.ProcessUserActionAsync(userAction);
            return new IpcResponse
            {
                Success = result.Success,
                Data = result,
                Error = result.Error,
                RequestId = request.RequestId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing user action");
            return new IpcResponse
            {
                Success = false,
                Error = ex.Message,
                RequestId = request.RequestId
            };
        }
    }

    private async Task<IpcResponse> HandleApplyTimerResolutionAsync(IpcRequest request)
    {
        var enable = request.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyTimerResolutionAsync(enable);
        return new IpcResponse
        {
            Success = result.Success,
            Data = result,
            Error = result.Error,
            RequestId = request.RequestId
        };
    }

    private async Task<IpcResponse> HandleShutdownAsync(IpcRequest request)
    {
        _logger.LogInformation("Received shutdown request from Electron application");

        // Respond immediately to confirm shutdown request received
        var response = new IpcResponse
        {
            Success = true,
            Data = "Shutdown initiated",
            RequestId = request.RequestId
        };

        // Initiate graceful shutdown after a brief delay to allow response to be sent
        _ = Task.Run(async () =>
        {
            await Task.Delay(500); // Give time for response to be sent
            _logger.LogInformation("Initiating graceful application shutdown");
            _hostLifetime.StopApplication();
        });

        return response;
    }
}
