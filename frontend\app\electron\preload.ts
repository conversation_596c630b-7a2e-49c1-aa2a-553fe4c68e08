import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define the API interface - simplified for stateless UI
export interface ElectronAPI {
  // Core backend communication
  sendBackendRequest: (request: any) => Promise<any>;

  // Basic service info (read-only)
  getBackendPort: () => Promise<number>;
  checkBackendStatus: () => Promise<boolean>;
  checkAuthServerStatus: () => Promise<boolean>;

  // Simple service management
  restartBackendService: () => Promise<boolean>;
  validateAllServices: () => Promise<any>;
  retryServiceConnection: () => Promise<any>;

  // System information
  platform: string;

  // App information
  getVersion: () => Promise<string>;
  getAppPath: (name: string) => Promise<string>;

  // Dialog methods
  showMessageBox: (options: any) => Promise<any>;
  showErrorBox: (title: string, content: string) => Promise<void>;
}

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
const electronAPI: ElectronAPI = {
  // Core backend communication
  sendBackendRequest: (request: any) => ipcRenderer.invoke('send-backend-request', request),

  // Basic service info (read-only)
  getBackendPort: () => ipcRenderer.invoke('get-backend-port'),
  checkBackendStatus: () => ipcRenderer.invoke('check-backend-status'),
  checkAuthServerStatus: () => ipcRenderer.invoke('check-auth-server-status'),

  // Simple service management
  restartBackendService: () => ipcRenderer.invoke('restart-backend-service'),
  validateAllServices: () => ipcRenderer.invoke('validate-all-services'),
  retryServiceConnection: () => ipcRenderer.invoke('retry-service-connection'),

  // System information
  platform: process.platform,

  // App information
  getVersion: () => ipcRenderer.invoke('get-app-version'),
  getAppPath: (name: string) => ipcRenderer.invoke('get-app-path', name),

  // Dialog methods
  showMessageBox: (options: any) => ipcRenderer.invoke('show-message-box', options),
  showErrorBox: (title: string, content: string) => ipcRenderer.invoke('show-error-box', title, content),
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Type declaration for the global window object
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
