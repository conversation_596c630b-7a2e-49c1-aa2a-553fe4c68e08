import express from 'express';
import cors from 'cors';

const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/api/health', (req, res) => {
  console.log('Health check requested');
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: 'development'
  });
});

// Mock login endpoint
app.post('/api/auth/login', (req, res) => {
  console.log('Login requested:', req.body);
  const { username, password } = req.body;

  // Simple mock validation
  if (!username || !password) {
    return res.status(400).json({
      success: false,
      error: 'Username and password are required'
    });
  }

  // Mock successful login for any non-empty credentials
  const mockUser = {
    id: '12345',
    username: username,
    email: `${username}@example.com`,
    role: 'User',
    isPremium: true
  };

  const mockToken = 'mock_access_token_' + Date.now();
  const mockRefreshToken = 'mock_refresh_token_' + Date.now();
  const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

  res.json({
    success: true,
    accessToken: mockToken,
    refreshToken: mockRefreshToken,
    expiresAt: expiresAt.toISOString(),
    user: mockUser
  });
});

// Mock register endpoint
app.post('/api/auth/register', (req, res) => {
  console.log('Register requested:', req.body);
  const { username, email, password } = req.body;

  if (!username || !email || !password) {
    return res.status(400).json({
      success: false,
      error: 'Username, email, and password are required'
    });
  }

  // Mock successful registration
  const mockUser = {
    id: '12345',
    username: username,
    email: email,
    role: 'User',
    isPremium: true
  };

  const mockToken = 'mock_access_token_' + Date.now();
  const mockRefreshToken = 'mock_refresh_token_' + Date.now();
  const expiresAt = new Date(Date.now() + 60 * 60 * 1000);

  res.json({
    success: true,
    accessToken: mockToken,
    refreshToken: mockRefreshToken,
    expiresAt: expiresAt.toISOString(),
    user: mockUser
  });
});

// Mock refresh endpoint
app.post('/api/auth/refresh', (req, res) => {
  console.log('Refresh requested:', req.body);
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.status(401).json({
      success: false,
      error: 'Refresh token is required'
    });
  }

  // Mock successful refresh
  const mockToken = 'mock_access_token_' + Date.now();
  const mockRefreshToken = 'mock_refresh_token_' + Date.now();
  const expiresAt = new Date(Date.now() + 60 * 60 * 1000);

  res.json({
    success: true,
    accessToken: mockToken,
    refreshToken: mockRefreshToken,
    expiresAt: expiresAt.toISOString()
  });
});

// Mock logout endpoint
app.post('/api/auth/logout', (req, res) => {
  console.log('Logout requested');
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Mock verify endpoint
app.get('/api/auth/verify', (req, res) => {
  console.log('Verify requested');
  res.json({
    isValid: true,
    user: {
      id: '12345',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'User',
      isPremium: true
    }
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Ghost Tweaker Mock API Server',
    version: '1.0.0',
    endpoints: {
      health: '/api/health',
      auth: '/api/auth'
    }
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// Start server
const server = app.listen(port, 'localhost', () => {
  console.log(`🚀 Mock Ghost Tweaker API Server is running on localhost:${port}`);
  console.log(`📊 Health check: http://localhost:${port}/api/health`);
  console.log(`🔐 Auth endpoints: http://localhost:${port}/api/auth`);
  console.log(`Server address: ${JSON.stringify(server.address())}`);
});

server.on('error', (err) => {
  console.error('Server error:', err);
});

server.on('connection', (socket) => {
  console.log('New connection established');
});
