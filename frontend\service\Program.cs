using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using GhostTweaker.Service.Services;
using GhostTweaker.Service.Models;

namespace GhostTweaker.Service;

public class Program
{
    public static async Task<int> Main(string[] args)
    {
        // For now, let's use a simple approach without System.CommandLine
        // Parse port from command line arguments manually
        int port = 3001;

        for (int i = 0; i < args.Length - 1; i++)
        {
            if (args[i] == "--port" && int.TryParse(args[i + 1], out int parsedPort))
            {
                port = parsedPort;
                break;
            }
        }

        await RunService(port);
        return 0;
    }

    private static async Task RunService(int port)
    {
        var builder = Host.CreateApplicationBuilder();

        // Configuration
        builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
        builder.Configuration.AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true);
        builder.Configuration.AddEnvironmentVariables();

        // Add services
        builder.Services.Configure<ServiceConfiguration>(options =>
        {
            options.Port = port;
            options.AuthServerUrl = builder.Configuration["AuthServer:Url"] ?? "http://localhost:3000";
        });

        builder.Services.AddHttpClient();
        builder.Services.AddSingleton<IAuthenticationService, AuthenticationService>();
        builder.Services.AddSingleton<ITimerResolutionService, TimerResolutionService>();
        builder.Services.AddSingleton<ISystemTweaksService, SystemTweaksService>();
        builder.Services.AddSingleton<IApplicationStateService, ApplicationStateService>();
        builder.Services.AddSingleton<IIpcServerService, IpcServerService>(); // Keep old service for now
        builder.Services.AddSingleton<INamedPipeIpcService, NamedPipeIpcService>(); // New Named Pipe service
        builder.Services.AddHostedService<GhostTweaker.Service.Services.BackgroundService>();

        // Logging
        builder.Services.AddLogging(logging =>
        {
            logging.AddConsole();
            logging.SetMinimumLevel(LogLevel.Information);
        });

        var host = builder.Build();

        try
        {
            await host.RunAsync();
        }
        catch (Exception ex)
        {
            var logger = host.Services.GetService<ILogger<Program>>();
            logger?.LogCritical(ex, "Application terminated unexpectedly");
            throw;
        }
    }
}
