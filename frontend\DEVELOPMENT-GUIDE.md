# Ghost Tweaker - Frontend Development Guide

## Overview

The Ghost Tweaker Frontend is a desktop application for Windows system optimization that combines an Electron-based user interface with a local .NET service for system operations.

## Directory Structure

```
frontend/
├── app/                     # Electron + Vue.js Application
│   ├── electron/           # Electron main process
│   ├── src/               # Vue.js frontend code
│   ├── package.json       # App dependencies
│   └── ...                # Build configs, assets, etc.
└── service/               # Local .NET Service
    ├── Services/          # Business logic
    ├── Models/            # Data models
    ├── Program.cs         # Entry point
    └── *.csproj          # .NET project
```

## Component Purposes

### Electron App (`app/`)
**What it contains**: The user interface and application orchestration
**Technology**: Electron + Vue.js 3 + TypeScript + TailwindCSS
**Components**:
- **electron/**: Main Electron process and preload scripts
- **src/**: Vue.js application with stores, views, and routing
- **package.json**: Frontend dependencies and build scripts

**Purpose**:
- Provides the desktop user interface
- Manages communication with the local .NET service
- Handles user authentication with external API
- Orchestrates the overall application workflow

### Local .NET Service (`service/`)
**What it contains**: System optimization and tweaking operations
**Technology**: .NET 8 + ASP.NET Core
**Components**:
- **Services/**: Business logic for system operations
- **Models/**: Data models and DTOs
- **Program.cs**: Service entry point and configuration

**Purpose**:
- Performs actual system tweaks and optimizations
- Provides secure local API for the Electron app
- Handles Windows-specific system operations
- Runs as a local service bundled with the desktop app

## Development Workflow

### Starting Development
```bash
# Install all dependencies
npm run install:all

# Start all components
npm run dev

# Or start individually:
npm run dev:api      # External API server
npm run dev:service  # Local .NET service  
npm run dev:app      # Electron desktop app
```

### Building for Production
```bash
# Build all components
npm run build

# Or build individually:
npm run build:api      # Build API server
npm run build:service  # Build .NET service
npm run build:app      # Build Electron app

# Create Windows installer
npm run dist:win
```

## Key Benefits of New Structure

### 1. **Clear Separation of Concerns**
- **Frontend**: Everything needed for the desktop application
- **Backend**: Everything needed for the external API server
- No confusion about what belongs where

### 2. **Deployment Clarity**
- **Desktop App**: Single installer includes both Electron app and .NET service
- **API Server**: Separate deployment for authentication infrastructure
- Each can be versioned and deployed independently

### 3. **Development Experience**
- Developers immediately understand the architecture
- Easy to work on specific components
- Clear dependency relationships
- Proper monorepo tooling support

### 4. **Scalability**
- Desktop app components stay together (UI + local service)
- API server can be scaled independently
- Room for future backend utilities in `backend/client/`
- Clean separation for team responsibilities

## Migration from Old Structure

### Old Structure Issues
```
gt-client/client/     # Confusing naming
gt-backend/          # Unclear if local or remote
gt-server/server/    # Redundant naming
```

### New Structure Benefits
```
frontend/app/        # Clear: desktop app UI
frontend/service/    # Clear: local system service
backend/server/      # Clear: external API server
```

## Working with the New Structure

### For Frontend Developers
- Work in `frontend/app/` for UI changes
- Understand that `frontend/service/` provides local system operations
- Both components deploy together as one desktop application

### For Backend Developers  
- Work in `backend/server/` for API changes
- This is deployed separately from the desktop application
- Focus on authentication and user management

### For DevOps/Deployment
- **Desktop Application**: Build and package `frontend/` as single installer
- **API Server**: Deploy `backend/server/` to cloud infrastructure
- Two separate deployment pipelines for two separate products

## Future Extensions

### Backend Client Utilities (`backend/client/`)
- Shared TypeScript types between frontend and backend
- API client libraries
- Common validation schemas
- Shared utilities for API communication

### Additional Services
- Could add `frontend/updater/` for auto-update service
- Could add `backend/analytics/` for usage analytics API
- Structure supports clean addition of new components

## Commands Reference

### Development
- `npm run dev` - Start all components
- `npm run dev:api` - Start external API server only
- `npm run dev:service` - Start local .NET service only  
- `npm run dev:app` - Start Electron desktop app only

### Building
- `npm run build` - Build all components
- `npm run build:api` - Build API server
- `npm run build:service` - Build .NET service
- `npm run build:app` - Build Electron app

### Utilities
- `npm run install:all` - Install all dependencies
- `npm run clean` - Clean all build artifacts
- `npm run init-db` - Initialize database with default users
- `npm run dist:win` - Create Windows installer

This new structure provides clarity, maintainability, and proper separation of concerns while supporting the existing functionality and future growth.
