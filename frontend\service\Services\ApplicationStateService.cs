using System.Text.Json;
using GhostTweaker.Service.Models;
using Microsoft.Extensions.Logging;

namespace GhostTweaker.Service.Services;

public class ApplicationStateService : IApplicationStateService
{
    private readonly ILogger<ApplicationStateService> _logger;
    private readonly ISystemTweaksService _tweaksService;
    private readonly IAuthenticationService _authService;
    private readonly string _stateFilePath;
    private readonly object _stateLock = new();

    private ApplicationState _currentState;

    public event EventHandler<ApplicationState>? StateChanged;

    public ApplicationStateService(
        ILogger<ApplicationStateService> logger,
        ISystemTweaksService tweaksService,
        IAuthenticationService authService)
    {
        _logger = logger;
        _tweaksService = tweaksService;
        _authService = authService;
        _stateFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "GhostTweaker", "application-state.json");
        _currentState = new ApplicationState();

        // Ensure state directory exists
        Directory.CreateDirectory(Path.GetDirectoryName(_stateFilePath)!);
    }

    public async Task<ApplicationState> GetApplicationStateAsync()
    {
        lock (_stateLock)
        {
            // Update timestamps
            _currentState.LastUpdated = DateTime.UtcNow;
            return CloneState(_currentState);
        }
    }

    public async Task<TweakStates> GetTweakStatesAsync()
    {
        lock (_stateLock)
        {
            return new TweakStates
            {
                NetworkTweaksEnabled = _currentState.TweakStates.NetworkTweaksEnabled,
                FpsBoostEnabled = _currentState.TweakStates.FpsBoostEnabled,
                AdvancedRegistryEnabled = _currentState.TweakStates.AdvancedRegistryEnabled,
                GameModeEnabled = _currentState.TweakStates.GameModeEnabled,
                LastUpdated = _currentState.TweakStates.LastUpdated
            };
        }
    }

    public async Task<TimerResolutionInfo> GetTimerResolutionInfoAsync()
    {
        lock (_stateLock)
        {
            return new TimerResolutionInfo
            {
                IsEnabled = _currentState.TimerResolution.IsEnabled,
                CurrentResolutionMs = _currentState.TimerResolution.CurrentResolutionMs,
                TargetResolutionMs = _currentState.TimerResolution.TargetResolutionMs,
                OriginalResolutionMs = _currentState.TimerResolution.OriginalResolutionMs,
                LastUpdated = _currentState.TimerResolution.LastUpdated
            };
        }
    }

    public async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        lock (_stateLock)
        {
            return new AuthenticationState
            {
                IsAuthenticated = _currentState.AuthState.IsAuthenticated,
                User = _currentState.AuthState.User,
                TokenExpiresAt = _currentState.AuthState.TokenExpiresAt,
                NeedsRefresh = _currentState.AuthState.NeedsRefresh,
                LastChecked = _currentState.AuthState.LastChecked
            };
        }
    }

    public async Task<ServiceStatus> GetServiceStatusAsync()
    {
        lock (_stateLock)
        {
            return new ServiceStatus
            {
                BackendServiceRunning = _currentState.ServiceStatus.BackendServiceRunning,
                AuthServerConnected = _currentState.ServiceStatus.AuthServerConnected,
                LastAuthServerCheck = _currentState.ServiceStatus.LastAuthServerCheck,
                LastError = _currentState.ServiceStatus.LastError
            };
        }
    }

    public async Task UpdateTweakStateAsync(string tweakType, bool enabled)
    {
        lock (_stateLock)
        {
            switch (tweakType.ToLowerInvariant())
            {
                case "network":
                    _currentState.TweakStates.NetworkTweaksEnabled = enabled;
                    break;
                case "fps":
                    _currentState.TweakStates.FpsBoostEnabled = enabled;
                    break;
                case "registry":
                    _currentState.TweakStates.AdvancedRegistryEnabled = enabled;
                    break;
                case "gamemode":
                    _currentState.TweakStates.GameModeEnabled = enabled;
                    break;
                default:
                    _logger.LogWarning("Unknown tweak type: {TweakType}", tweakType);
                    return;
            }

            _currentState.TweakStates.LastUpdated = DateTime.UtcNow;
            _currentState.LastUpdated = DateTime.UtcNow;
        }

        await SaveStateAsync();
        NotifyStateChanged();
    }

    public async Task UpdateTimerResolutionAsync(TimerResolutionInfo timerInfo)
    {
        lock (_stateLock)
        {
            _currentState.TimerResolution = new TimerResolutionInfo
            {
                IsEnabled = timerInfo.IsEnabled,
                CurrentResolutionMs = timerInfo.CurrentResolutionMs,
                TargetResolutionMs = timerInfo.TargetResolutionMs,
                OriginalResolutionMs = timerInfo.OriginalResolutionMs,
                LastUpdated = DateTime.UtcNow
            };
            _currentState.LastUpdated = DateTime.UtcNow;
        }

        await SaveStateAsync();
        NotifyStateChanged();
    }

    public async Task UpdateAuthenticationStateAsync(AuthenticationState authState)
    {
        lock (_stateLock)
        {
            _currentState.AuthState = new AuthenticationState
            {
                IsAuthenticated = authState.IsAuthenticated,
                User = authState.User,
                TokenExpiresAt = authState.TokenExpiresAt,
                NeedsRefresh = authState.NeedsRefresh,
                LastChecked = DateTime.UtcNow
            };
            _currentState.LastUpdated = DateTime.UtcNow;
        }

        await SaveStateAsync();
        NotifyStateChanged();
    }

    public async Task UpdateServiceStatusAsync(ServiceStatus serviceStatus)
    {
        lock (_stateLock)
        {
            _currentState.ServiceStatus = new ServiceStatus
            {
                BackendServiceRunning = serviceStatus.BackendServiceRunning,
                AuthServerConnected = serviceStatus.AuthServerConnected,
                LastAuthServerCheck = DateTime.UtcNow,
                LastError = serviceStatus.LastError
            };
            _currentState.LastUpdated = DateTime.UtcNow;
        }

        await SaveStateAsync();
        NotifyStateChanged();
    }

    public async Task<TweakResult> ProcessUserActionAsync(UserAction action)
    {
        _logger.LogInformation("Processing user action: {ActionType}", action.ActionType);

        try
        {
            return action.ActionType.ToLowerInvariant() switch
            {
                "toggle-network-tweaks" => await ProcessNetworkTweaksAction(action),
                "toggle-fps-boost" => await ProcessFpsBoostAction(action),
                "toggle-advanced-registry" => await ProcessAdvancedRegistryAction(action),
                "toggle-game-mode" => await ProcessGameModeAction(action),
                "toggle-timer-resolution" => await ProcessTimerResolutionAction(action),
                "optimize-ram" => await ProcessRamOptimizationAction(action),
                "create-restore-point" => await ProcessCreateRestorePointAction(action),
                _ => new TweakResult
                {
                    Success = false,
                    Error = $"Unknown action type: {action.ActionType}"
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing user action: {ActionType}", action.ActionType);
            return new TweakResult
            {
                Success = false,
                Error = ex.Message
            };
        }
    }

    public async Task SaveStateAsync()
    {
        try
        {
            ApplicationState stateToSave;
            lock (_stateLock)
            {
                stateToSave = CloneState(_currentState);
            }

            var json = JsonSerializer.Serialize(stateToSave, new JsonSerializerOptions
            {
                WriteIndented = true
            });

            await File.WriteAllTextAsync(_stateFilePath, json);
            _logger.LogDebug("Application state saved to {FilePath}", _stateFilePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save application state");
        }
    }

    public async Task LoadStateAsync()
    {
        try
        {
            if (!File.Exists(_stateFilePath))
            {
                _logger.LogInformation("No saved state found, using default state");
                return;
            }

            var json = await File.ReadAllTextAsync(_stateFilePath);
            var loadedState = JsonSerializer.Deserialize<ApplicationState>(json);

            if (loadedState != null)
            {
                lock (_stateLock)
                {
                    _currentState = loadedState;
                    _currentState.LastUpdated = DateTime.UtcNow;
                }
                _logger.LogInformation("Application state loaded from {FilePath}", _stateFilePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load application state, using default state");
        }
    }

    private ApplicationState CloneState(ApplicationState state)
    {
        // Simple deep clone using JSON serialization
        var json = JsonSerializer.Serialize(state);
        return JsonSerializer.Deserialize<ApplicationState>(json)!;
    }

    private void NotifyStateChanged()
    {
        try
        {
            StateChanged?.Invoke(this, CloneState(_currentState));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error notifying state change");
        }
    }

    private async Task<TweakResult> ProcessNetworkTweaksAction(UserAction action)
    {
        var enable = action.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyNetworkTweaksAsync(enable);

        if (result.Success)
        {
            await UpdateTweakStateAsync("network", enable);
        }

        return result;
    }

    private async Task<TweakResult> ProcessFpsBoostAction(UserAction action)
    {
        var enable = action.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyFpsBoostTweaksAsync(enable);

        if (result.Success)
        {
            await UpdateTweakStateAsync("fps", enable);
        }

        return result;
    }

    private async Task<TweakResult> ProcessAdvancedRegistryAction(UserAction action)
    {
        var enable = action.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyAdvancedRegistryTweaksAsync(enable);

        if (result.Success)
        {
            await UpdateTweakStateAsync("registry", enable);
        }

        return result;
    }

    private async Task<TweakResult> ProcessGameModeAction(UserAction action)
    {
        var enable = action.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyGameModeTweaksAsync(enable);

        if (result.Success)
        {
            await UpdateTweakStateAsync("gamemode", enable);
        }

        return result;
    }

    private async Task<TweakResult> ProcessTimerResolutionAction(UserAction action)
    {
        var enable = action.Parameters?.GetValueOrDefault("enable")?.ToString() == "true";
        var result = await _tweaksService.ApplyTimerResolutionAsync(enable);

        if (result.Success)
        {
            // Update timer resolution state
            var timerInfo = await _tweaksService.GetTimerResolutionInfoAsync();
            await UpdateTimerResolutionAsync(timerInfo);
        }

        return result;
    }

    private async Task<TweakResult> ProcessRamOptimizationAction(UserAction action)
    {
        return await _tweaksService.ApplyRamOptimizationAsync();
    }

    private async Task<TweakResult> ProcessCreateRestorePointAction(UserAction action)
    {
        var description = action.Parameters?.GetValueOrDefault("description")?.ToString()
            ?? $"Ghost Tweaker - {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
        return await _tweaksService.CreateSystemRestorePointAsync(description);
    }
}
