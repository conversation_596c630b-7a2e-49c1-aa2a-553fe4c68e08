import bcrypt from 'bcrypt';
import User from '../models/User';
import connectDB from '../config/db';

const initializeDatabase = async () => {
  try {
    console.log('🔄 Initializing database...');
    
    // Connect to database
    await connectDB();
    
    // Check if admin user already exists
    const existingAdmin = await User.findOne({ 
      $or: [
        { email: '<EMAIL>' },
        { username: 'admin' }
      ]
    });

    if (existingAdmin) {
      console.log('✅ Admin user already exists');
      return;
    }

    // Create default admin user
    const hashedPassword = await bcrypt.hash('admin123', 12);
    const adminUser = new User({
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      isPremium: true
    });

    await adminUser.save();
    console.log('✅ Default admin user created successfully');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('⚠️  Please change the default password after first login!');

    // Create a test user as well
    const testUserExists = await User.findOne({
      $or: [
        { email: '<EMAIL>' },
        { username: 'testuser' }
      ]
    });

    if (!testUserExists) {
      const testHashedPassword = await bcrypt.hash('test123', 12);
      const testUser = new User({
        username: 'testuser',
        email: '<EMAIL>',
        password: testHashedPassword,
        role: 'user',
        isPremium: false
      });

      await testUser.save();
      console.log('✅ Test user created successfully');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: test123');
    }

  } catch (error) {
    console.error('❌ Error initializing database:', error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
};

// Run if called directly
if (require.main === module) {
  initializeDatabase();
}

export default initializeDatabase;
