{"compilerOptions": {"outDir": "dist-electron", "module": "CommonJS", "target": "ES2020", "lib": ["ES2020"], "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "resolveJsonModule": true, "declaration": false, "sourceMap": true, "types": ["node"], "noEmit": false}, "include": ["electron/**/*"], "exclude": ["node_modules", "dist", "dist-electron"]}