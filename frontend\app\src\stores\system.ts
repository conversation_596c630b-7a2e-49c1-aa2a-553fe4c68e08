import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

// Types
export interface SystemHealth {
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  processCount: number
  uptime: string
  timestamp: string
}

export interface TweakResult {
  success: boolean
  message: string
  error?: string
  data?: any
}

export interface ApplicationState {
  tweakStates: {
    networkTweaksEnabled: boolean
    fpsBoostEnabled: boolean
    advancedRegistryEnabled: boolean
    gameModeEnabled: boolean
  }
  timerResolution: {
    isEnabled: boolean
    currentResolutionMs: number
    targetResolutionMs: number
    originalResolutionMs?: number
  }
  systemHealth?: SystemHealth
  authState: {
    isAuthenticated: boolean
    user?: any
  }
  serviceStatus: {
    backendServiceRunning: boolean
    authServerConnected: boolean
  }
}

export const useSystemStore = defineStore('system', () => {
  // UI state
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Consolidated system state (cached from backend)
  const applicationState = ref<ApplicationState | null>(null)

  // Computed properties for easy access
  const isSystemReady = computed(() => {
    const serviceStatus = applicationState.value?.serviceStatus
    return serviceStatus?.backendServiceRunning && serviceStatus?.authServerConnected
  })

  const systemHealth = computed(() => applicationState.value?.systemHealth)
  const serviceStatus = computed(() => applicationState.value?.serviceStatus)

  // Simplified API client
  const sendBackendRequest = async (method: string, parameters: any = {}): Promise<any> => {
    if (!window.electronAPI?.sendBackendRequest) {
      throw new Error('Backend communication not available')
    }

    const response = await window.electronAPI.sendBackendRequest({
      method,
      parameters,
      requestId: crypto.randomUUID()
    })

    if (!response.success) {
      throw new Error(response.error || 'Backend request failed')
    }

    return response.data
  }

  // Refresh system state from backend
  const refreshSystemState = async (): Promise<void> => {
    try {
      applicationState.value = await sendBackendRequest('get-application-state')
    } catch (err: any) {
      console.error('Failed to refresh system state:', err)
      error.value = err.message
    }
  }

  // Get fresh application state (for compatibility)
  const getApplicationState = async (): Promise<ApplicationState> => {
    await refreshSystemState()
    return applicationState.value || {} as ApplicationState
  }

  const restartBackendService = async (): Promise<boolean> => {
    try {
      return await window.electronAPI?.restartBackendService() ?? false
    } catch (err) {
      return false
    }
  }

  // User action processor - delegates to backend
  const processUserAction = async (actionType: string, parameters: any = {}): Promise<TweakResult> => {
    try {
      isLoading.value = true

      const result = await sendBackendRequest('process-user-action', {
        actionType,
        parameters
      })

      return result
    } catch (err: any) {
      return { success: false, message: '', error: err.message }
    } finally {
      isLoading.value = false
    }
  }

  // Legacy individual tweak methods (for backward compatibility)
  const applyNetworkTweaks = async (enable: boolean): Promise<TweakResult> => {
    return processUserAction('toggle-network-tweaks', { enable })
  }

  const applyFpsBoostTweaks = async (enable: boolean): Promise<TweakResult> => {
    return processUserAction('toggle-fps-boost', { enable })
  }

  const applyRamOptimization = async (): Promise<TweakResult> => {
    return processUserAction('optimize-ram')
  }

  const applyAdvancedRegistryTweaks = async (enable: boolean): Promise<TweakResult> => {
    return processUserAction('toggle-advanced-registry', { enable })
  }

  const applyGameModeTweaks = async (enable: boolean): Promise<TweakResult> => {
    return processUserAction('toggle-game-mode', { enable })
  }

  const applyTimerResolution = async (enable: boolean): Promise<TweakResult> => {
    return processUserAction('toggle-timer-resolution', { enable })
  }

  const createSystemRestorePoint = async (description: string): Promise<TweakResult> => {
    return processUserAction('create-restore-point', { description })
  }

  const clearError = () => {
    error.value = null
  }

  // Simple initialization
  const initializeSystem = async () => {
    try {
      // Initialize by refreshing system state
      await refreshSystemState()
    } catch (err: any) {
      error.value = err.message
    }
  }

  return {
    // State
    isLoading,
    error,
    applicationState,
    isSystemReady,
    systemHealth,
    serviceStatus,

    // Methods
    refreshSystemState,
    getApplicationState,
    restartBackendService,
    processUserAction,

    // Legacy methods (for compatibility)
    applyNetworkTweaks,
    applyFpsBoostTweaks,
    applyRamOptimization,
    applyAdvancedRegistryTweaks,
    applyGameModeTweaks,
    applyTimerResolution,
    createSystemRestorePoint,

    // Utilities
    clearError,
    initializeSystem
  }
})
