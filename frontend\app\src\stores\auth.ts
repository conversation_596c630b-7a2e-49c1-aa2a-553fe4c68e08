import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface User {
  id: string
  username: string
  email: string
  role: string
  isPremium: boolean
}

export interface AuthResponse {
  success: boolean
  accessToken?: string
  refreshToken?: string
  expiresAt?: string
  user?: User
  error?: string
}

export const useAuthStore = defineStore('auth', () => {
  // UI state
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Auth state cache (refreshed from backend)
  const authState = ref<{ isAuthenticated: boolean; user?: User } | null>(null)

  // Computed properties for easy access
  const isAuthenticated = computed(() => authState.value?.isAuthenticated ?? false)
  const user = computed(() => authState.value?.user ?? null)

  // Simplified API client
  const sendBackendRequest = async (method: string, parameters: any = {}): Promise<any> => {
    if (!window.electronAPI?.sendBackendRequest) {
      throw new Error('Backend communication not available')
    }

    const response = await window.electronAPI.sendBackendRequest({
      method,
      parameters,
      requestId: crypto.randomUUID()
    })

    if (!response.success) {
      throw new Error(response.error || 'Backend request failed')
    }

    return response.data
  }

  // Refresh auth state from backend
  const refreshAuthState = async (): Promise<void> => {
    try {
      const appState = await sendBackendRequest('get-application-state')
      authState.value = appState.authState
    } catch (err: any) {
      console.error('Failed to refresh auth state:', err)
      authState.value = { isAuthenticated: false }
      error.value = err.message
    }
  }

  // Check if user is currently authenticated (simplified)
  const checkAuthStatus = async (): Promise<boolean> => {
    await refreshAuthState()
    return isAuthenticated.value
  }

  // Login user (simplified)
  const login = async (username: string, password: string, rememberMe = false): Promise<AuthResponse> => {
    try {
      isLoading.value = true
      error.value = null

      const result = await sendBackendRequest('login', {
        username,
        password,
        rememberMe
      })

      // Refresh auth state if login successful
      if (result.success) {
        await refreshAuthState()
      }

      return result
    } catch (err: any) {
      error.value = err.message
      return {
        success: false,
        error: err.message
      }
    } finally {
      isLoading.value = false
    }
  }

  // Register user (simplified)
  const register = async (username: string, email: string, password: string): Promise<AuthResponse> => {
    try {
      isLoading.value = true
      error.value = null

      return await sendBackendRequest('register', {
        username,
        email,
        password
      })
    } catch (err: any) {
      error.value = err.message
      return {
        success: false,
        error: err.message
      }
    } finally {
      isLoading.value = false
    }
  }

  // Logout user (simplified)
  const logout = async (): Promise<void> => {
    try {
      isLoading.value = true
      error.value = null

      await sendBackendRequest('logout')
    } catch (err: any) {
      error.value = err.message
    } finally {
      // Always clear local auth state on logout
      authState.value = { isAuthenticated: false }
      isLoading.value = false
    }
  }

  // Clear error
  const clearError = () => {
    error.value = null
  }

  return {
    // State
    isLoading,
    error,
    isAuthenticated,
    user,

    // Methods
    checkAuthStatus,
    refreshAuthState,
    login,
    register,
    logout,
    clearError
  }
})
