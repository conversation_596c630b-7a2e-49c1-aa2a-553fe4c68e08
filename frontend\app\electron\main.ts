import { app, BrowserWindow, ipc<PERSON>ain, dialog, shell } from 'electron';
import { join } from 'path';
import { spawn, ChildProcess } from 'child_process';
import { existsSync } from 'fs';
import { Socket } from 'net';
import { randomUUID } from 'crypto';
import { config } from 'dotenv';

// __dirname is available globally in CommonJS

// Load environment variables from .env.development in development mode
const isDev = !app.isPackaged;
if (isDev) {
  config({ path: join(__dirname, '../.env.development') });
}

// Keep a global reference of the window object
let mainWindow: BrowserWindow | null = null;
let backendService: ChildProcess | null = null;

const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL;

// Backend service configuration
const BACKEND_SERVICE_PORT = 3001;
const BACKEND_SERVICE_PATH = isDev
  ? join(__dirname, '../../service/bin/Debug/net8.0/win-x64/GhostTweaker.Service.exe')
  : join(process.resourcesPath, 'service', 'GhostTweaker.Service.exe');

// Track if we need elevated privileges
let needsElevation = false;

// Function to check if backend service is actually running
async function checkBackendServiceRunning(): Promise<boolean> {
  return new Promise((resolve) => {
    const socket = new Socket();

    socket.setTimeout(2000);

    socket.on('connect', () => {
      socket.destroy();
      resolve(true);
    });

    socket.on('timeout', () => {
      socket.destroy();
      resolve(false);
    });

    socket.on('error', () => {
      resolve(false);
    });

    try {
      socket.connect(BACKEND_SERVICE_PORT, 'localhost');
    } catch (error) {
      resolve(false);
    }
  });
}

console.log('🔍 Environment check:');
console.log('   NODE_ENV:', process.env.NODE_ENV);
console.log('   VITE_DEV_SERVER_URL:', process.env.VITE_DEV_SERVER_URL);
console.log('   isDev:', isDev);
console.log('   BACKEND_SERVICE_PATH:', BACKEND_SERVICE_PATH);

// Auth server configuration
const AUTH_SERVER_PORT = 3000;
const AUTH_SERVER_URL = `http://localhost:${AUTH_SERVER_PORT}`;

let authServerProcess: ChildProcess | null = null;

function createWindow(): void {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: join(__dirname, 'preload.cjs'),
    },
    titleBarStyle: 'default',
    show: false, // Don't show until ready
    icon: join(__dirname, '../public/favicon.ico'),
  });

  // Load the app
  if (VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(VITE_DEV_SERVER_URL);
    // Open DevTools in development
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  } else {
    mainWindow.loadFile(join(__dirname, '../dist/index.html'));
  }

  // Listen for service error events from main process
  mainWindow.webContents.on('ipc-message', (event, channel, ...args) => {
    if (channel === 'service-error') {
      console.log('Service error received in main process:', args[0]);
    }
  });

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show();
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Auth server health checking is now handled by the backend service
// This function is no longer needed as all external service communication
// goes through the backend service

async function startBackendService(): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      if (!existsSync(BACKEND_SERVICE_PATH)) {
        console.error('Backend service not found at:', BACKEND_SERVICE_PATH);
        resolve(false);
        return;
      }

      // Set the working directory to the service directory so it can find appsettings.json
      const serviceWorkingDir = isDev
        ? join(__dirname, '../../service')
        : join(process.resourcesPath, 'service');

      // Start the backend service with elevated privileges using PowerShell
      console.log('🔐 Starting backend service with administrator privileges...');

      const powershellCommand = `Start-Process -FilePath "${BACKEND_SERVICE_PATH}" -ArgumentList "--port=${BACKEND_SERVICE_PORT}" -WorkingDirectory "${serviceWorkingDir}" -Verb RunAs -WindowStyle Hidden`;

      const launcher = spawn('powershell.exe', ['-Command', powershellCommand], {
        detached: false,
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: serviceWorkingDir,
      });

      launcher.stdout?.on('data', (data) => {
        console.log('PowerShell launcher output:', data.toString());
      });

      launcher.stderr?.on('data', (data) => {
        console.error('PowerShell launcher error:', data.toString());
      });

      launcher.on('close', (code) => {
        console.log(`PowerShell launcher exited with code ${code}`);
        if (code === 0) {
          console.log('✅ Backend service launched successfully with elevated privileges');
        } else {
          console.error('❌ Failed to launch backend service with elevated privileges');
        }
      });

      launcher.on('error', (error) => {
        console.error('Failed to start PowerShell launcher:', error);

        // Check if it's a UAC cancellation
        if (error.message.includes('cancelled') || error.message.includes('denied')) {
          console.error('❌ UAC prompt was cancelled or denied');
          console.error('🔐 Administrator privileges are required for Ghost Tweaker to function properly');
        }

        resolve(false);
      });

      // Wait for the launcher to complete, then check if service is actually running
      setTimeout(async () => {
        console.log('🔍 Checking if backend service is actually running...');
        const isRunning = await checkBackendServiceRunning();
        if (isRunning) {
          console.log('✅ Backend service confirmed running');
          // Set a placeholder process object to indicate service is running
          // This object needs to have the same interface as ChildProcess for compatibility
          backendService = {
            pid: -1,
            killed: false,
            kill: (signal?: string | number) => {
              console.log('🛑 Attempting to kill elevated backend service...');
              // Use taskkill to stop the elevated process
              const killProcess = spawn('taskkill', ['/F', '/IM', 'GhostTweaker.Service.exe'], {
                stdio: 'pipe'
              });

              killProcess.on('close', (code) => {
                if (code === 0) {
                  console.log('✅ Backend service terminated successfully');
                } else {
                  console.log('ℹ️ Backend service may not have been running or already terminated');
                }
              });

              killProcess.on('error', (error) => {
                console.error('❌ Failed to terminate backend service:', error.message);
              });

              return true;
            }
          } as any;
          resolve(true);
        } else {
          console.error('❌ Backend service is not responding after launch attempt');
          resolve(false);
        }
      }, 4000); // Give more time for elevated service to start

    } catch (error) {
      console.error('Error starting backend service:', error);
      resolve(false);
    }
  });
}

function stopAuthServer(): void {
  // Auth server is external, no need to stop it
  console.log('Auth server is external, not managed by this application');
}

async function stopBackendServiceGracefully(): Promise<void> {
  if (!backendService || backendService.killed) {
    console.log('ℹ️ No backend service to stop or already stopped');
    return;
  }

  console.log('🛑 Attempting graceful shutdown of backend service...');

  try {
    // First, try graceful shutdown via IPC
    const socket = new Socket();
    socket.setTimeout(3000);

    const shutdownPromise = new Promise<boolean>((resolve) => {
      socket.on('connect', () => {
        console.log('📡 Connected to backend service for shutdown request');

        const shutdownRequest = {
          method: 'shutdown',
          requestId: Date.now().toString(),
          parameters: {}
        };

        const requestData = JSON.stringify(shutdownRequest) + '\n';
        socket.write(requestData);

        socket.on('data', (data) => {
          try {
            const response = JSON.parse(data.toString().trim());
            if (response.success) {
              console.log('✅ Graceful shutdown request acknowledged by backend service');
              resolve(true);
            } else {
              console.log('⚠️ Backend service declined shutdown request');
              resolve(false);
            }
          } catch (error) {
            console.log('⚠️ Invalid response from backend service during shutdown');
            resolve(false);
          }
          socket.destroy();
        });
      });

      socket.on('timeout', () => {
        console.log('⏰ Timeout connecting to backend service for shutdown');
        socket.destroy();
        resolve(false);
      });

      socket.on('error', () => {
        console.log('❌ Error connecting to backend service for shutdown');
        resolve(false);
      });
    });

    socket.connect(BACKEND_SERVICE_PORT, 'localhost');
    const gracefulShutdownSucceeded = await shutdownPromise;

    if (gracefulShutdownSucceeded) {
      // Wait for the service to actually shut down
      console.log('⏳ Waiting for backend service to shut down gracefully...');

      // Check if service is still running for up to 5 seconds
      for (let i = 0; i < 10; i++) {
        await new Promise(resolve => setTimeout(resolve, 500));
        const isStillRunning = await checkBackendServiceRunning();
        if (!isStillRunning) {
          console.log('✅ Backend service shut down gracefully');
          backendService = null;
          return;
        }
      }

      console.log('⚠️ Backend service did not shut down within timeout, falling back to force termination');
    }
  } catch (error) {
    console.log('❌ Error during graceful shutdown attempt:', error);
  }

  // Fallback to force termination
  console.log('🔨 Using force termination as fallback...');
  await stopBackendServiceProcesses();
  backendService = null;
}

function stopBackendService(): void {
  if (backendService && !backendService.killed) {
    console.log('🛑 Stopping backend service...');
    try {
      backendService.kill('SIGTERM');
      console.log('✅ Backend service stop signal sent');
    } catch (error) {
      console.error('❌ Error stopping backend service:', error);
    }
    backendService = null;
  } else {
    console.log('ℹ️ No backend service to stop or already stopped');
  }
}

async function stopBackendServiceProcesses(): Promise<void> {
  return new Promise((resolve) => {
    console.log('🛑 Stopping any existing backend service processes...');

    // Use taskkill to stop any running GhostTweaker.Service.exe processes
    const killCommand = 'taskkill /F /IM GhostTweaker.Service.exe';

    const killProcess = spawn('cmd', ['/c', killCommand], {
      stdio: 'pipe'
    });

    killProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Existing backend service processes stopped');
      } else {
        console.log('ℹ️ No existing backend service processes found (or already stopped)');
      }
      resolve();
    });

    killProcess.on('error', (error) => {
      console.log('ℹ️ Could not stop existing processes (may not be running):', error.message);
      resolve();
    });

    // Timeout after 3 seconds
    setTimeout(() => {
      killProcess.kill();
      resolve();
    }, 3000);
  });
}

async function startBackendServiceElevated(): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      console.log('🔐 Starting backend service with elevated privileges...');

      if (!existsSync(BACKEND_SERVICE_PATH)) {
        console.error('Backend service executable not found:', BACKEND_SERVICE_PATH);
        resolve(false);
        return;
      }

      // Stop existing service first
      stopBackendService();

      // Set the working directory to the service directory
      const serviceWorkingDir = isDev
        ? join(__dirname, '../../service')
        : join(process.resourcesPath, 'service');

      // Use PowerShell to start the service with elevated privileges
      const powershellCommand = `Start-Process -FilePath "${BACKEND_SERVICE_PATH}" -ArgumentList "--port=${BACKEND_SERVICE_PORT}" -WorkingDirectory "${serviceWorkingDir}" -Verb RunAs -WindowStyle Hidden`;

      backendService = spawn('powershell.exe', ['-Command', powershellCommand], {
        detached: true,
        stdio: 'ignore',
      });

      backendService.on('close', (code) => {
        console.log(`Elevated backend service launcher exited with code ${code}`);
      });

      backendService.on('error', (error) => {
        console.error('Failed to start elevated backend service:', error);
        resolve(false);
      });

      // Give the service time to start
      setTimeout(() => {
        resolve(true);
      }, 3000);

    } catch (error) {
      console.error('Error starting elevated backend service:', error);
      resolve(false);
    }
  });
}

// App event handlers
app.whenReady().then(async () => {
  console.log('🚀 Starting Ghost Tweaker application...');

  // STRICT BACKEND SERVICE DEPENDENCY CHECK - Must succeed before anything else
  console.log('⚙️ Starting local backend service...');
  const serviceStarted = await startBackendService();

  if (!serviceStarted) {
    console.error('❌ Local backend service failed to start - CRITICAL FAILURE');
    console.error('🛑 APPLICATION CANNOT START WITHOUT BACKEND SERVICE');

    // Check if the service file exists to provide more specific error message
    const serviceExists = existsSync(BACKEND_SERVICE_PATH);

    if (serviceExists) {
      console.error('📍 Service executable found at:', BACKEND_SERVICE_PATH);
      console.error('🔧 Troubleshooting steps:');
      console.error('   1. Check if port', BACKEND_SERVICE_PORT, 'is available');
      console.error('   2. Verify the service has proper permissions');
      console.error('   3. Check Windows Event Logs for service errors');
      console.error('   4. Try running as administrator');
      console.error('   5. Ensure .NET 8.0 runtime is installed');
    } else {
      console.error('📍 Service executable NOT FOUND at:', BACKEND_SERVICE_PATH);
      console.error('🔧 Troubleshooting steps:');
      console.error('   1. Rebuild the backend service project');
      console.error('   2. Ensure the service was compiled successfully');
      console.error('   3. Check if the build output directory exists');
      console.error('   4. Verify the application installation is complete');
    }

    console.log('🛑 Application exiting silently due to backend service failure');
    app.quit();
    return;
  }

  console.log('✅ Backend service started successfully');
  console.log('🖥️ Creating application window...');
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', async () => {
  console.log('🛑 Stopping services...');
  await stopBackendServiceGracefully();
  stopAuthServer();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', async (event) => {
  console.log('🛑 Application is quitting, stopping services...');

  // Prevent immediate quit to allow graceful shutdown
  event.preventDefault();

  try {
    await stopBackendServiceGracefully();
    stopAuthServer();
    console.log('✅ All services stopped, quitting application');
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
  } finally {
    // Force quit after cleanup attempt
    app.exit(0);
  }
});

// IPC handlers
ipcMain.handle('get-backend-port', () => {
  return BACKEND_SERVICE_PORT;
});

ipcMain.handle('get-auth-server-port', () => {
  return AUTH_SERVER_PORT;
});

ipcMain.handle('get-auth-server-url', () => {
  return AUTH_SERVER_URL;
});

ipcMain.handle('check-backend-status', async () => {
  // Check if service is actually running by attempting to connect
  const isRunning = await checkBackendServiceRunning();
  console.log(`🔍 Backend status check: ${isRunning ? 'RUNNING' : 'NOT RUNNING'}`);
  return isRunning;
});

ipcMain.handle('check-auth-server-status', async () => {
  // Simple delegation to backend service - no business logic
  try {
    const response = await sendTcpRequest({
      method: 'check-auth-server-health',
      parameters: {},
      requestId: randomUUID()
    });
    return response.success ? (response.data?.IsHealthy ?? response.data?.isHealthy ?? false) : false;
  } catch (error: any) {
    return false;
  }
});

ipcMain.handle('restart-backend-service', async () => {
  console.log('🔄 Restarting backend service with elevated privileges...');

  // First, try to stop any existing service processes
  await stopBackendServiceProcesses();

  // Wait a moment for cleanup
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Start with elevated privileges
  const result = await startBackendService();
  console.log(result ? '✅ Backend service restarted with elevation' : '❌ Failed to restart backend service');
  return result;
});

// Pure TCP client function to communicate with .NET backend service (no HTTP support)
async function sendTcpRequest(request: any): Promise<any> {
  return new Promise((resolve, reject) => {
    const client = new Socket();
    const timeout = setTimeout(() => {
      client.destroy();
      reject(new Error('TCP request timeout'));
    }, 10000); // 10 second timeout

    client.connect(BACKEND_SERVICE_PORT, 'localhost', () => {
      const requestJson = JSON.stringify(request);

      client.write(requestJson);
    });

    let responseData = '';
    client.on('data', (data) => {
      responseData += data.toString();

    });

    client.on('end', () => {
      clearTimeout(timeout);

      try {
        const response = JSON.parse(responseData);
        resolve(response);
      } catch (error) {
        console.error('❌ Failed to parse TCP response:', error);
        reject(new Error(`Failed to parse response: ${error}`));
      }
    });

    client.on('error', (error) => {
      clearTimeout(timeout);
      console.error('❌ TCP client error:', error);
      reject(error);
    });
  });
}

ipcMain.handle('send-backend-request', async (event, request) => {
  try {
    const response = await sendTcpRequest(request);
    return response;
  } catch (error: any) {
    console.error('❌ Backend request failed:', error.message);
    return {
      success: false,
      error: error.message,
      requestId: request.requestId
    };
  }
});

ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow!, options);
  return result;
});

// Handler to restart backend service with elevated privileges
ipcMain.handle('restart-backend-elevated', async () => {
  try {
    console.log('🔐 Restarting backend service with elevated privileges...');
    const success = await startBackendServiceElevated();

    if (success) {
      needsElevation = false;
      return { success: true, message: 'Backend service restarted with elevated privileges' };
    } else {
      return { success: false, error: 'Failed to restart backend service with elevated privileges' };
    }
  } catch (error: any) {
    console.error('Error restarting backend service with elevation:', error);
    return { success: false, error: error.message };
  }
});

// Simplified service validation - delegate to backend
ipcMain.handle('validate-all-services', async () => {
  try {
    const response = await sendTcpRequest({
      method: 'get-application-state',
      parameters: {},
      requestId: randomUUID()
    });
    return {
      success: response.success,
      services: response.success ? { backend: true, auth: true } : { backend: false, auth: false }
    };
  } catch (error) {
    return {
      success: false,
      error: { type: 'backend', message: 'Backend service not available' }
    };
  }
});

ipcMain.handle('retry-service-connection', async () => {
  // Simple retry - just restart the backend service
  try {
    const serviceStarted = await startBackendService();
    return { success: serviceStarted };
  } catch (error) {
    return { success: false, error: { message: 'Failed to restart service' } };
  }
});

ipcMain.handle('show-error-box', (event, title, content) => {
  dialog.showErrorBox(title, content);
});

ipcMain.handle('get-app-path', (event, name) => {
  return app.getPath(name as any);
});

// Security: Prevent new window creation
app.on('web-contents-created', (_, contents) => {
  contents.setWindowOpenHandler(() => {
    return { action: 'deny' };
  });
});
