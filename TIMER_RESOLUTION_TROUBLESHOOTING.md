# Timer Resolution Troubleshooting Guide

## 🔍 Issue: Toggle Button Not Working

You mentioned that clicking the timer resolution toggle shows a brief loading indicator but doesn't actually toggle the state. Here's how to diagnose and fix this issue:

## 📋 Step-by-Step Troubleshooting

### Step 1: Access the Debug Panel
1. **Start the application** (both frontend and backend service)
2. **Login** to the dashboard
3. **Click the user menu** (top right corner)
4. **Select "🔧 Debug Panel"**

### Step 2: Check System Status
In the debug panel, look at the **System Status** section:

- ✅ **Backend Connected**: Should be "Yes" 
- ✅ **Auth Server**: Should be "Connected"
- ✅ **System Ready**: Should be "Ready"
- ✅ **Backend Port**: Should show a port number (e.g., 5000)

### Step 3: Test Timer Resolution Info
1. Click **"📊 Get Timer Info"** button
2. Check the **Debug Log** for messages
3. Look at the **Timer Resolution Info** panel for current values

### Step 4: Test Toggle Functionality
1. Click **"🎯 Toggle Timer Resolution"** button
2. Watch the **Debug Log** for detailed messages
3. Check if the **Timer Resolution Info** updates

## 🚨 Common Issues and Solutions

### Issue 1: Backend Service Not Running
**Symptoms:**
- Backend Connected: ❌ No
- System Ready: ❌ Not Ready
- Backend Port: Not Set

**Solution:**
```powershell
# Navigate to the service directory
cd frontend/service

# Start the backend service
dotnet run
```

### Issue 2: Electron IPC Not Available
**Symptoms:**
- Debug log shows: "⚠️ Backend IPC communication not available, using mock data"
- Toggle works but shows "(MOCK MODE)" in messages

**Solution:**
- Restart the Electron application
- Make sure you're running the app via `bun run dev` not just the Vue dev server

### Issue 3: Permission Issues (Windows)
**Symptoms:**
- Backend connected but timer resolution operations fail
- Error messages about "privileges" or "permission denied"

**Solution:**
- **Run as Administrator**: Right-click the application and select "Run as administrator"
- Timer resolution changes require elevated privileges on Windows

### Issue 4: Port Communication Issues
**Symptoms:**
- Backend shows as connected but requests fail
- Timeout errors in debug log

**Solution:**
```powershell
# Check if the backend service is listening
netstat -an | findstr :5000

# Restart both frontend and backend
```

## 🔧 Manual Testing Steps

### Test 1: Check Current Timer Resolution
```powershell
# Open PowerShell as Administrator and run:
# This will show current timer resolution (if you have tools available)
```

### Test 2: Verify Backend Service
1. Open browser to `http://localhost:5000/health` (replace 5000 with your backend port)
2. Should return JSON with system health info

### Test 3: Test Direct API Call
```javascript
// Open browser console on the debug page and run:
window.electronAPI.sendBackendRequest({
  method: 'get-timer-resolution-info',
  parameters: {},
  requestId: 'test-123'
}).then(console.log).catch(console.error)
```

## 📊 Expected Behavior

### When Working Correctly:
1. **Initial State**: Timer resolution typically shows ~15.6ms (system default)
2. **After Enable**: Should change to 1.0ms, toggle shows as active
3. **After Disable**: Should return to higher value (~15.6ms), toggle shows as inactive
4. **State Persistence**: Settings should persist across app restarts

### Debug Log Messages:
```
✅ Timer resolution info: 15.6ms (enabled: false)
🎯 Attempting to enable timer resolution...
✅ Timer resolution enabled successfully (1ms). Previous: 15.6ms, Current: 1.0ms
```

## 🛠️ Advanced Debugging

### Enable Verbose Logging
The debug panel now includes detailed console logging. Open browser DevTools (F12) to see:
- IPC communication details
- Backend request/response data
- Error stack traces

### Check Network Tab
In DevTools Network tab, look for:
- WebSocket connections (if using WebSocket IPC)
- HTTP requests to backend service
- Failed requests or timeouts

## 📞 Next Steps

1. **Try the debug panel first** - it will show you exactly what's happening
2. **Check the browser console** (F12) for detailed error messages
3. **Verify the backend service is running** and accessible
4. **Run as Administrator** if on Windows
5. **Report specific error messages** from the debug log for further assistance

## 🎯 Quick Fix Checklist

- [ ] Backend service is running (`dotnet run` in service directory)
- [ ] Application started with `bun run dev` (not just Vue dev server)
- [ ] Running as Administrator on Windows
- [ ] Debug panel shows "System Ready: ✅ Ready"
- [ ] Timer resolution info loads successfully
- [ ] No error messages in debug log

If all items are checked and it still doesn't work, the debug log will show the specific error that needs to be addressed.
