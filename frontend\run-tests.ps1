# Ghost Tweaker - Test Runner Script
# This script runs the .NET service tests for the Timer Resolution feature

Write-Host "🧪 Running Ghost Tweaker Service Tests..." -ForegroundColor Cyan
Write-Host ""

# Check if .NET SDK is installed
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET SDK not found. Please install .NET 8 SDK." -ForegroundColor Red
    exit 1
}

# Navigate to test directory
$testDir = Join-Path $PSScriptRoot "service.tests"
if (-not (Test-Path $testDir)) {
    Write-Host "❌ Test directory not found: $testDir" -ForegroundColor Red
    exit 1
}

Set-Location $testDir

Write-Host "📁 Test Directory: $testDir" -ForegroundColor Yellow
Write-Host ""

# Restore test dependencies
Write-Host "📦 Restoring test dependencies..." -ForegroundColor Yellow
dotnet restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to restore dependencies" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Dependencies restored successfully" -ForegroundColor Green
Write-Host ""

# Build test project
Write-Host "🔨 Building test project..." -ForegroundColor Yellow
dotnet build --no-restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to build test project" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Test project built successfully" -ForegroundColor Green
Write-Host ""

# Run tests
Write-Host "🚀 Running Timer Resolution Tests..." -ForegroundColor Cyan
Write-Host "⚠️  Note: Some tests may require Administrator privileges on Windows" -ForegroundColor Yellow
Write-Host ""

dotnet test --no-build --verbosity normal --logger "console;verbosity=detailed"

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "✅ All tests completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Test Summary:" -ForegroundColor Cyan
    Write-Host "  • Unit tests verify service methods work correctly" -ForegroundColor White
    Write-Host "  • Integration tests verify timer resolution workflow" -ForegroundColor White
    Write-Host "  • Error handling tests verify graceful failure recovery" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 To test manually:" -ForegroundColor Yellow
    Write-Host "  1. Run the application as Administrator" -ForegroundColor White
    Write-Host "  2. Toggle the Timer Resolution switch in the dashboard" -ForegroundColor White
    Write-Host "  3. Verify the current resolution changes from ~15.6ms to 1.0ms" -ForegroundColor White
    Write-Host "  4. Check that the toggle state persists correctly" -ForegroundColor White
} else {
    Write-Host ""
    Write-Host "❌ Some tests failed. Check the output above for details." -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Common issues:" -ForegroundColor Yellow
    Write-Host "  • Timer resolution tests require Windows" -ForegroundColor White
    Write-Host "  • Some operations may require Administrator privileges" -ForegroundColor White
    Write-Host "  • Ensure no other applications are modifying timer resolution" -ForegroundColor White
}

Write-Host ""
Write-Host "🏁 Test run completed." -ForegroundColor Cyan
