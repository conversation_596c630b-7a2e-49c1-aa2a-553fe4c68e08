<template>
  <div class="flex items-center justify-between p-4 bg-black/20 rounded-lg">
    <div>
      <h3 class="font-medium text-white">{{ title }}</h3>
      <p class="text-sm text-slate-400">{{ description }}</p>
    </div>
    <button 
      @click="$emit('toggle')" 
      :disabled="disabled" 
      :class="[
        'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
        enabled ? 'bg-purple-600' : 'bg-gray-600',
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      ]"
    >
      <span :class="[
        'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
        enabled ? 'translate-x-6' : 'translate-x-1'
      ]"></span>
    </button>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  title: string
  description: string
  enabled: boolean
  disabled?: boolean
}>()

defineEmits<{
  toggle: []
}>()
</script>
