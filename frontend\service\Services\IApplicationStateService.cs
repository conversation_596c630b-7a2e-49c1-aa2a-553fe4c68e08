using GhostTweaker.Service.Models;

namespace GhostTweaker.Service.Services;

public interface IApplicationStateService
{
    // State retrieval
    Task<ApplicationState> GetApplicationStateAsync();
    Task<TweakStates> GetTweakStatesAsync();
    Task<TimerResolutionInfo> GetTimerResolutionInfoAsync();
    Task<AuthenticationState> GetAuthenticationStateAsync();
    Task<ServiceStatus> GetServiceStatusAsync();

    // State updates
    Task UpdateTweakStateAsync(string tweakType, bool enabled);
    Task UpdateTimerResolutionAsync(TimerResolutionInfo timerInfo);
    Task UpdateAuthenticationStateAsync(AuthenticationState authState);
    Task UpdateServiceStatusAsync(ServiceStatus serviceStatus);

    // User actions
    Task<TweakResult> ProcessUserActionAsync(UserAction action);

    // State persistence
    Task SaveStateAsync();
    Task LoadStateAsync();

    // Events for state changes
    event EventHandler<ApplicationState>? StateChanged;
}
