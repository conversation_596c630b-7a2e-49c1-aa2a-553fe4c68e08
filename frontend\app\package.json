{"name": "ghost-tweaker-app", "version": "1.0.0", "description": "Ghost Tweaker Desktop Application", "main": "dist-electron/main.cjs", "private": true, "type": "module", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "wait-on http://localhost:5173 && electron .", "build": "npm run build:vite && npm run build:electron", "build:vite": "run-p  \"build-only {@}\" --", "build:electron": "bunx tsc -p tsconfig.electron.json && node -e \"const fs = require('fs'); const path = require('path'); const dir = './dist-electron'; fs.readdirSync(dir).filter(f => f.endsWith('.js')).forEach(f => fs.renameSync(path.join(dir, f), path.join(dir, f.replace('.js', '.cjs'))));\"", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "preview": "vite preview", "electron": "electron .", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "@vueuse/core": "^13.5.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron": "^37.2.2", "lucide-vue-next": "^0.525.0", "pinia": "^2.3.1", "reka-ui": "^2.3.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "concurrently": "^9.2.0", "dotenv": "^17.0.1", "electron-builder": "^26.0.12", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10", "wait-on": "^8.0.1"}, "build": {"appId": "com.ghosttweaker.app", "productName": "<PERSON> Tweaker", "directories": {"output": "dist"}, "files": ["dist/**/*", "dist-electron/**/*", "node_modules/**/*"], "extraResources": [{"from": "../service/bin/Release/net8.0/publish/", "to": "service/", "filter": ["**/*"]}], "win": {"target": "nsis", "icon": "public/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}