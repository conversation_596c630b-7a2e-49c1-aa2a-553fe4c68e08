{"name": "ghost-tweaker-api-server", "version": "1.0.0", "description": "Ghost Tweaker External Authentication API Server", "module": "src/index.ts", "type": "module", "private": true, "scripts": {"dev": "bun --watch src/index.ts", "start": "bun src/index.ts", "build": "tsc", "init-db": "bun src/scripts/init-db.ts", "lint": "eslint src --ext .ts", "test": "echo \"No tests specified\" && exit 0"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^22.15.32", "typescript": "^5.8.0"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "bcrypt": "^6.0.0", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1"}}