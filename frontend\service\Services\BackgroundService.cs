using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using GhostTweaker.Service.Models;

namespace GhostTweaker.Service.Services;

public class BackgroundService : Microsoft.Extensions.Hosting.BackgroundService
{
    private readonly ILogger<BackgroundService> _logger;
    private readonly IIpcServerService _ipcServerService; // Back to TCP for compatibility
    private readonly IApplicationStateService _stateService;
    private readonly IHttpClientFactory _httpClientFactory;

    public BackgroundService(
        ILogger<BackgroundService> logger,
        IIpcServerService ipcServerService,
        IApplicationStateService stateService,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _ipcServerService = ipcServerService;
        _stateService = stateService;
        _httpClientFactory = httpClientFactory;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("Ghost Tweaker Background Service starting");

            // Load application state from disk
            await _stateService.LoadStateAsync();
            _logger.LogInformation("Application state loaded");

            // Start the IPC server (TCP without HTTP support)
            await _ipcServerService.StartAsync(stoppingToken);

            // Initialize service status
            await UpdateServiceStatusAsync();

            // Keep the service running with periodic status updates
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // Update service status every 10 seconds
                    await UpdateServiceStatusAsync();
                    await Task.Delay(10000, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during periodic service status update");
                    await Task.Delay(5000, stoppingToken); // Shorter delay on error
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Background service was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in background service");
            throw;
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Ghost Tweaker Background Service stopping");

        try
        {
            // Save application state before stopping
            await _stateService.SaveStateAsync();
            _logger.LogInformation("Application state saved");

            await _ipcServerService.StopAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping services");
        }

        await base.StopAsync(cancellationToken);
        _logger.LogInformation("Ghost Tweaker Background Service stopped");
    }

    private async Task UpdateServiceStatusAsync()
    {
        try
        {
            // Check auth server status
            bool authServerConnected = false;
            try
            {
                using var httpClient = _httpClientFactory.CreateClient();
                httpClient.Timeout = TimeSpan.FromSeconds(5);
                var response = await httpClient.GetAsync("http://localhost:3000/api/health");
                authServerConnected = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogDebug("Auth server health check failed: {Error}", ex.Message);
                authServerConnected = false;
            }

            // Update service status in application state
            var serviceStatus = new ServiceStatus
            {
                BackendServiceRunning = true, // If we're running this code, backend is running
                AuthServerConnected = authServerConnected,
                LastAuthServerCheck = DateTime.UtcNow,
                LastError = authServerConnected ? null : "Auth server not reachable"
            };

            await _stateService.UpdateServiceStatusAsync(serviceStatus);

            _logger.LogDebug("Service status updated - Backend: {Backend}, Auth: {Auth}",
                serviceStatus.BackendServiceRunning, serviceStatus.AuthServerConnected);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating service status");
        }
    }
}
