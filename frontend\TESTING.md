# Ghost Tweaker - Testing Guide

## Testing Checklist

### Prerequisites
- [ ] Node.js 18+ installed
- [ ] .NET 8 SDK installed
- [ ] MongoDB running (local or cloud)
- [ ] Administrator privileges for system tweaks

### Initial Setup Testing

1. **Installation**
   ```bash
   npm run install:all
   ```
   - [ ] All dependencies install without errors
   - [ ] No security vulnerabilities reported

2. **Database Initialization**
   ```bash
   npm run init-db
   ```
   - [ ] MongoDB connection successful
   - [ ] Default admin user created
   - [ ] Default test user created

3. **Development Environment**
   ```bash
   npm run dev
   ```
   - [ ] Auth server starts on port 3000
   - [ ] Backend service starts on port 3001
   - [ ] Electron app launches successfully
   - [ ] No console errors in any component

### Authentication Flow Testing

#### Login Testing
- [ ] Login with admin credentials (<EMAIL> / admin123)
- [ ] Login with test credentials (<EMAIL> / test123)
- [ ] Login with username instead of email
- [ ] "Remember Me" checkbox functionality
- [ ] Invalid credentials show proper error message
- [ ] Empty fields show validation errors

#### Registration Testing
- [ ] Register new user with valid data
- [ ] Password confirmation validation
- [ ] Email format validation
- [ ] Username uniqueness validation
- [ ] Password minimum length validation
- [ ] Successful registration auto-login

#### Token Management
- [ ] JWT tokens stored securely
- [ ] Automatic token refresh before expiration
- [ ] Logout clears stored tokens
- [ ] Invalid/expired tokens redirect to login
- [ ] Persistent login state across app restarts

### System Integration Testing

#### Service Status
- [ ] Backend service status indicator accurate
- [ ] Auth server status indicator accurate
- [ ] Service restart functionality works
- [ ] Error handling for service failures

#### System Health Monitoring
- [ ] CPU usage displays correctly
- [ ] Memory usage displays correctly
- [ ] Disk usage displays correctly
- [ ] Process count displays correctly
- [ ] Real-time updates every 5 seconds
- [ ] Refresh button updates immediately

### System Tweaks Testing

⚠️ **Warning**: These tests modify system settings. Create a system restore point first!

#### Timer Resolution
- [ ] Enable timer resolution tweak
- [ ] Disable timer resolution tweak
- [ ] Success/error messages display
- [ ] Toggle state persists correctly

#### Network Optimization
- [ ] Enable network tweaks
- [ ] Disable network tweaks
- [ ] Registry changes applied correctly
- [ ] Revert functionality works

#### FPS Boost
- [ ] Enable FPS boost tweaks
- [ ] Services stopped/started correctly
- [ ] Power plan changed to high performance
- [ ] Disable functionality restores services

#### Advanced Registry
- [ ] Enable advanced registry tweaks
- [ ] SystemResponsiveness set to 0
- [ ] GPU scheduling enabled
- [ ] Disable functionality restores defaults

#### RAM Optimization
- [ ] RAM optimization executes
- [ ] Working set cleared
- [ ] Standby list cleared (requires admin)
- [ ] Success message displayed

#### System Restore
- [ ] Create restore point functionality
- [ ] Restore point created successfully
- [ ] Appropriate description set

### UI/UX Testing

#### Responsive Design
- [ ] Application scales properly
- [ ] Minimum window size respected
- [ ] All elements visible and accessible
- [ ] Proper spacing and alignment

#### Visual Feedback
- [ ] Loading states show during operations
- [ ] Success messages display correctly
- [ ] Error messages are user-friendly
- [ ] Toggle switches animate smoothly

#### Navigation
- [ ] Login/register tab switching
- [ ] Authentication guards work
- [ ] User menu functionality
- [ ] Logout redirects to login

### Error Handling Testing

#### Network Errors
- [ ] Auth server offline handling
- [ ] Backend service offline handling
- [ ] MongoDB connection errors
- [ ] Timeout handling

#### Permission Errors
- [ ] Non-admin user limitations
- [ ] Registry access denied handling
- [ ] Service management errors
- [ ] File system permission errors

#### Invalid Data
- [ ] Malformed JWT tokens
- [ ] Invalid API responses
- [ ] Corrupted configuration files
- [ ] Missing dependencies

### Performance Testing

#### Startup Performance
- [ ] Application starts within 10 seconds
- [ ] Services initialize properly
- [ ] No memory leaks on startup
- [ ] CPU usage reasonable

#### Runtime Performance
- [ ] Smooth UI interactions
- [ ] Real-time updates don't lag
- [ ] Memory usage stable over time
- [ ] No performance degradation

### Security Testing

#### Authentication Security
- [ ] Passwords properly hashed
- [ ] JWT tokens properly signed
- [ ] Refresh token rotation works
- [ ] Session timeout enforced

#### IPC Security
- [ ] No direct Node.js access in renderer
- [ ] Context isolation enabled
- [ ] Preload script whitelist enforced
- [ ] No eval() or unsafe execution

#### System Security
- [ ] Registry changes require admin
- [ ] Service modifications protected
- [ ] File system access controlled
- [ ] No privilege escalation

### Build and Distribution Testing

#### Development Build
- [ ] `npm run build` completes successfully
- [ ] All components build without errors
- [ ] Built files are correct size
- [ ] No missing dependencies

#### Production Distribution
- [ ] `npm run dist:win` creates installer
- [ ] Installer runs without errors
- [ ] Application installs correctly
- [ ] All files included in package

#### Post-Installation
- [ ] Application starts from Start Menu
- [ ] All services initialize correctly
- [ ] User data directory created
- [ ] Uninstaller works properly

### Cross-Environment Testing

#### Windows Versions
- [ ] Windows 10 compatibility
- [ ] Windows 11 compatibility
- [ ] Different user account types
- [ ] Various system configurations

#### Hardware Configurations
- [ ] Different CPU architectures
- [ ] Various memory configurations
- [ ] Multiple monitor setups
- [ ] Different graphics cards

### Regression Testing

After any code changes, verify:
- [ ] Authentication flow still works
- [ ] System tweaks apply correctly
- [ ] UI remains responsive
- [ ] No new console errors
- [ ] Performance not degraded

## Common Issues and Solutions

### Issue: Backend service won't start
**Solution**: 
- Check .NET 8 SDK installation
- Verify port 3001 availability
- Run as administrator

### Issue: Auth server connection failed
**Solution**:
- Verify MongoDB is running
- Check connection string
- Ensure port 3000 is available

### Issue: System tweaks don't apply
**Solution**:
- Run application as administrator
- Check Windows version compatibility
- Verify registry permissions

### Issue: Electron app won't start
**Solution**:
- Clear node_modules: `npm run clean && npm run install:all`
- Check for port conflicts
- Verify Electron installation

## Test Data

### Test Users
```
Admin User:
- Email: <EMAIL>
- Username: admin
- Password: admin123
- Role: admin
- Premium: true

Test User:
- Email: <EMAIL>
- Username: testuser
- Password: test123
- Role: user
- Premium: false
```

### Test Scenarios
1. **New User Journey**: Register → Login → Explore features
2. **Returning User**: Login with stored tokens → Use application
3. **Admin Workflow**: Login as admin → Apply all tweaks → Monitor system
4. **Error Recovery**: Simulate failures → Verify graceful handling

## Automated Testing (Future)

### Unit Tests
- Authentication service methods
- System tweak functions
- Vue component behavior
- Utility functions

### Integration Tests
- Complete authentication flow
- Backend service communication
- Database operations
- IPC message handling

### E2E Tests
- Full application workflow
- Cross-component interactions
- Real system modifications
- User interface automation

## Performance Benchmarks

### Acceptable Metrics
- Startup time: < 10 seconds
- Memory usage: < 200MB idle
- CPU usage: < 5% idle
- Response time: < 1 second for UI actions

### Monitoring
- Task Manager for resource usage
- DevTools for frontend performance
- .NET performance counters for backend
- MongoDB profiler for database queries
