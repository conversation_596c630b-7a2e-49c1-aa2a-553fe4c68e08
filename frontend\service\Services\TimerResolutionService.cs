using System.Runtime.InteropServices;
using GhostTweaker.Service.Models;
using Microsoft.Extensions.Logging;

namespace GhostTweaker.Service.Services;

public class TimerResolutionService : ITimerResolutionService
{
    private readonly ILogger<TimerResolutionService> _logger;
    private readonly object _lock = new();
    
    private bool _isEnabled = false;
    private double _originalResolution = 15.625; // Default Windows timer resolution
    private double _targetResolution = 1.0; // Target high resolution
    private bool _disposed = false;

    public bool IsTimerResolutionEnabled => _isEnabled;
    public event EventHandler<TimerResolutionInfo>? TimerResolutionChanged;

    // Windows API declarations
    [DllImport("winmm.dll", SetLastError = true)]
    private static extern uint timeBeginPeriod(uint uPeriod);

    [DllImport("winmm.dll", SetLastError = true)]
    private static extern uint timeEndPeriod(uint uPeriod);

    [DllImport("ntdll.dll", SetLastError = true)]
    private static extern int NtQueryTimerResolution(out uint MinimumResolution, out uint MaximumResolution, out uint CurrentResolution);

    public TimerResolutionService(ILogger<TimerResolutionService> logger)
    {
        _logger = logger;
        
        // Get initial system resolution
        Task.Run(async () =>
        {
            try
            {
                _originalResolution = await GetCurrentSystemResolutionAsync();
                _logger.LogInformation("Timer resolution service initialized. Current system resolution: {Resolution}ms", _originalResolution);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get initial timer resolution");
            }
        });
    }

    public async Task<TimerResolutionInfo> GetTimerResolutionInfoAsync()
    {
        try
        {
            var currentResolution = await GetCurrentSystemResolutionAsync();
            
            lock (_lock)
            {
                return new TimerResolutionInfo
                {
                    IsEnabled = _isEnabled,
                    CurrentResolutionMs = currentResolution,
                    TargetResolutionMs = _targetResolution,
                    OriginalResolutionMs = _originalResolution,
                    LastUpdated = DateTime.UtcNow
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get timer resolution info");
            throw;
        }
    }

    public async Task<TweakResult> ApplyTimerResolutionAsync(bool enable)
    {
        try
        {
            lock (_lock)
            {
                if (_disposed)
                {
                    return new TweakResult
                    {
                        Success = false,
                        Error = "Timer resolution service has been disposed"
                    };
                }

                if (enable == _isEnabled)
                {
                    return new TweakResult
                    {
                        Success = true,
                        Message = $"Timer resolution is already {(enable ? "enabled" : "disabled")}"
                    };
                }
            }

            var previousResolution = await GetCurrentSystemResolutionAsync();
            
            if (enable)
            {
                // Enable high resolution timer
                var result = timeBeginPeriod((uint)_targetResolution);
                if (result != 0)
                {
                    _logger.LogError("Failed to set timer resolution. Error code: {ErrorCode}", result);
                    return new TweakResult
                    {
                        Success = false,
                        Error = $"Failed to set timer resolution. Windows API error: {result}"
                    };
                }

                lock (_lock)
                {
                    _isEnabled = true;
                }

                _logger.LogInformation("Timer resolution enabled: {TargetResolution}ms", _targetResolution);
            }
            else
            {
                // Disable high resolution timer
                var result = timeEndPeriod((uint)_targetResolution);
                if (result != 0)
                {
                    _logger.LogWarning("Failed to restore timer resolution. Error code: {ErrorCode}", result);
                    // Don't return error here as the operation might still be partially successful
                }

                lock (_lock)
                {
                    _isEnabled = false;
                }

                _logger.LogInformation("Timer resolution disabled, restored to system default");
            }

            // Get the new resolution to verify the change
            var newResolution = await GetCurrentSystemResolutionAsync();
            
            var timerInfo = new TimerResolutionInfo
            {
                IsEnabled = enable,
                CurrentResolutionMs = newResolution,
                TargetResolutionMs = _targetResolution,
                OriginalResolutionMs = _originalResolution,
                LastUpdated = DateTime.UtcNow
            };

            // Notify listeners of the change
            TimerResolutionChanged?.Invoke(this, timerInfo);

            return new TweakResult
            {
                Success = true,
                Message = enable 
                    ? $"Timer resolution enabled successfully ({newResolution}ms). Previous: {previousResolution}ms, Current: {newResolution}ms"
                    : $"Timer resolution disabled successfully ({newResolution}ms). Previous: {previousResolution}ms, Current: {newResolution}ms",
                Data = new Dictionary<string, object>
                {
                    ["previousResolution"] = previousResolution,
                    ["currentResolution"] = newResolution,
                    ["targetResolution"] = _targetResolution,
                    ["isEnabled"] = enable
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying timer resolution: {Enable}", enable);
            return new TweakResult
            {
                Success = false,
                Error = ex.Message
            };
        }
    }

    public async Task<double> GetCurrentSystemResolutionAsync()
    {
        try
        {
            // Use NtQueryTimerResolution to get the current timer resolution
            var result = NtQueryTimerResolution(out uint minResolution, out uint maxResolution, out uint currentResolution);
            
            if (result != 0)
            {
                _logger.LogWarning("Failed to query timer resolution via NtQueryTimerResolution. Using fallback method.");
                // Fallback to a reasonable default
                return _isEnabled ? _targetResolution : _originalResolution;
            }

            // Convert from 100-nanosecond units to milliseconds
            var resolutionMs = currentResolution / 10000.0;
            
            _logger.LogDebug("Current timer resolution: {Resolution}ms (raw: {RawResolution})", resolutionMs, currentResolution);
            
            return resolutionMs;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get current system timer resolution");
            // Return a reasonable fallback
            return _isEnabled ? _targetResolution : _originalResolution;
        }
    }

    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            lock (_lock)
            {
                if (_isEnabled)
                {
                    _logger.LogInformation("Disposing timer resolution service, restoring system default");
                    
                    // Restore system default timer resolution
                    var result = timeEndPeriod((uint)_targetResolution);
                    if (result != 0)
                    {
                        _logger.LogWarning("Failed to restore timer resolution during disposal. Error code: {ErrorCode}", result);
                    }
                    
                    _isEnabled = false;
                }
                
                _disposed = true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during timer resolution service disposal");
        }
    }
}
