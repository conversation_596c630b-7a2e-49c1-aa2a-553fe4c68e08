<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 p-6">
    <div class="max-w-4xl mx-auto">
      <div class="bg-black/30 backdrop-blur-sm rounded-xl border border-white/10 p-6">
        <h1 class="text-2xl font-bold text-white mb-6">🔧 Debug Panel</h1>
        
        <!-- System Status -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div class="bg-black/20 rounded-lg p-4">
            <h2 class="text-lg font-semibold text-white mb-3">System Status</h2>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-slate-400">Backend Connected:</span>
                <span :class="systemStore.isBackendConnected ? 'text-green-400' : 'text-red-400'">
                  {{ systemStore.isBackendConnected ? '✅ Yes' : '❌ No' }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-slate-400">Auth Server:</span>
                <span :class="systemStore.isAuthServerConnected ? 'text-green-400' : 'text-red-400'">
                  {{ systemStore.isAuthServerConnected ? '✅ Connected' : '❌ Disconnected' }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-slate-400">System Ready:</span>
                <span :class="systemStore.isSystemReady ? 'text-green-400' : 'text-red-400'">
                  {{ systemStore.isSystemReady ? '✅ Ready' : '❌ Not Ready' }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-slate-400">Backend Port:</span>
                <span class="text-white">{{ systemStore.backendPort || 'Not Set' }}</span>
              </div>
            </div>
          </div>
          

        </div>
        
        <!-- Actions -->
        <div class="bg-black/20 rounded-lg p-4 mb-6">
          <h2 class="text-lg font-semibold text-white mb-4">Actions</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button @click="refreshStatus" :disabled="isLoading" class="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg transition-colors">
              {{ isLoading ? '🔄 Loading...' : '🔄 Refresh Status' }}
            </button>

          </div>
        </div>
        
        <!-- Debug Log -->
        <div class="bg-black/20 rounded-lg p-4">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-white">Debug Log</h2>
            <button @click="clearLog" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
              Clear Log
            </button>
          </div>
          <div class="bg-black/40 rounded p-3 max-h-96 overflow-y-auto">
            <div v-if="debugLog.length === 0" class="text-slate-400 text-sm">
              No debug messages yet. Click the buttons above to test functionality.
            </div>
            <div v-for="(entry, index) in debugLog" :key="index" class="text-sm mb-2 font-mono">
              <span class="text-slate-400">{{ entry.timestamp }}</span>
              <span :class="getLogColor(entry.level)" class="ml-2">{{ entry.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useSystemStore } from '@/stores/system'

const systemStore = useSystemStore()
const isLoading = ref(false)
const debugLog = ref<Array<{timestamp: string, level: string, message: string}>>([])

const addLog = (level: string, message: string) => {
  debugLog.value.push({
    timestamp: new Date().toLocaleTimeString(),
    level,
    message
  })
  // Keep only last 50 entries
  if (debugLog.value.length > 50) {
    debugLog.value = debugLog.value.slice(-50)
  }
}

const getLogColor = (level: string) => {
  switch (level) {
    case 'error': return 'text-red-400'
    case 'warn': return 'text-yellow-400'
    case 'success': return 'text-green-400'
    case 'info': return 'text-blue-400'
    default: return 'text-white'
  }
}

const clearLog = () => {
  debugLog.value = []
}

const refreshStatus = async () => {
  isLoading.value = true
  addLog('info', '🔄 Refreshing system status...')
  
  try {
    await systemStore.initializeSystem()
    addLog('success', '✅ System status refreshed successfully')
  } catch (error: any) {
    addLog('error', `❌ Failed to refresh status: ${error.message}`)
  } finally {
    isLoading.value = false
  }
}



onMounted(async () => {
  addLog('info', '🚀 Debug panel loaded')
  await refreshStatus()
})
</script>
