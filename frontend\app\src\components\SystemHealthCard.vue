<template>
  <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
    <div class="flex items-center">
      <div :class="['p-2 rounded-lg', iconBgClass]">
        <component :is="icon" :class="['w-6 h-6', iconClass]" />
      </div>
      <div class="ml-4">
        <p class="text-sm font-medium text-slate-300">{{ label }}</p>
        <p class="text-2xl font-bold text-white">{{ value }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  label: string
  value: string | number
  icon: any
  color: 'blue' | 'green' | 'purple' | 'yellow'
}>()

const iconBgClass = computed(() => {
  const colors = {
    blue: 'bg-blue-500/20',
    green: 'bg-green-500/20',
    purple: 'bg-purple-500/20',
    yellow: 'bg-yellow-500/20'
  }
  return colors[props.color]
})

const iconClass = computed(() => {
  const colors = {
    blue: 'text-blue-400',
    green: 'text-green-400',
    purple: 'text-purple-400',
    yellow: 'text-yellow-400'
  }
  return colors[props.color]
})
</script>
